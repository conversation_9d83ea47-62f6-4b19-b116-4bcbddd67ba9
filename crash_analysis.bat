@echo off
echo ========================================
echo 闪退分析脚本
echo ========================================

set LOG_FILE=crash_analysis_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
set LOG_FILE=%LOG_FILE: =0%

echo 日志将保存到: %LOG_FILE%
echo.

echo 1. 清除旧日志
adb logcat -c

echo 2. 开始收集系统信息...
echo ======== 设备信息 ======== > %LOG_FILE%
adb shell getprop ro.build.version.release >> %LOG_FILE%
adb shell getprop ro.product.model >> %LOG_FILE%
adb shell getprop ro.build.version.sdk >> %LOG_FILE%
echo. >> %LOG_FILE%

echo ======== 已安装应用 ======== >> %LOG_FILE%
adb shell pm list packages | findstr /i "azur" >> %LOG_FILE%
echo. >> %LOG_FILE%

echo ======== 内存信息 ======== >> %LOG_FILE%
adb shell cat /proc/meminfo | findstr /i "memtotal memfree" >> %LOG_FILE%
echo. >> %LOG_FILE%

echo 3. 请在另一个窗口启动应用，然后按任意键开始监控日志...
pause

echo 4. 开始监控应用日志（按Ctrl+C停止）...
echo ======== 应用日志 ======== >> %LOG_FILE%

echo 监控中... 请操作应用直到闪退发生
adb logcat -v time | findstr /i "fatal error exception androidruntime bilibili yostar azurlane" >> %LOG_FILE%

echo.
echo 日志已保存到: %LOG_FILE%
echo 请检查该文件中的错误信息
pause
