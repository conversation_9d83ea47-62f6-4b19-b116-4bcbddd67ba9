@echo off
chcp 65001 >nul
title Force Install WebView Fixed APK
color 0A

echo ========================================
echo Force Install WebView Fixed APK
echo ========================================

set PATH=%PATH%;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools

cd apk_build

echo [STEP 1] Checking WebView fixed APK...
if not exist "com.bilibili.azurlane-webview-fixed.apk" (
    echo ERROR: WebView fixed APK not found!
    pause
    exit /b 1
)

echo Found WebView fixed APK: com.bilibili.azurlane-webview-fixed.apk
dir "com.bilibili.azurlane-webview-fixed.apk"

echo [STEP 2] Force uninstalling old version...
adb shell pm uninstall com.bilibili.azurlane
echo Old version uninstalled (or was not installed)

echo [STEP 3] Installing WebView fixed APK...
echo This may take a moment...
adb install -r com.bilibili.azurlane-webview-fixed.apk

if %errorlevel% eq 0 (
    echo SUCCESS: WebView fixed APK installed!
    
    echo [STEP 4] Verifying installation...
    adb shell pm list packages | findstr bilibili
    
    echo [STEP 5] Checking APK path...
    adb shell pm path com.bilibili.azurlane
    
    echo.
    echo ========================================
    echo Installation completed successfully!
    echo ========================================
    echo.
    echo The WebView crash fix has been applied.
    echo Please test the app now - it should not crash after 3 seconds.
    
) else (
    echo ERROR: Installation failed!
    echo.
    echo This might be due to signature issues.
    echo Try manual installation:
    echo 1. Copy com.bilibili.azurlane-webview-fixed.apk to desktop
    echo 2. Drag it into MuMu emulator
    echo 3. Click install when prompted
)

cd ..
pause
