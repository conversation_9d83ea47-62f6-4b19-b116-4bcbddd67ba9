@echo off
chcp 65001 >nul
title Rebuild APK Without NDK
color 0A

echo ========================================
echo Rebuilding APK Without NDK
echo ========================================

echo [INFO] WebView crash fix has been applied successfully
echo [INFO] Now rebuilding APK without NDK dependencies

echo [STEP 1] Checking if APK build directory exists...
if not exist "apk_build\com.bilibili.AzurLane" (
    echo [ERROR] APK build directory not found!
    pause
    exit /b 1
)

echo [STEP 2] Using apktool to rebuild APK...
echo Rebuilding APK with WebView fixes...

REM Use apktool to rebuild the APK
apktool b apk_build\com.bilibili.AzurLane -o com.bilibili.azurlane-fixed.apk

if %errorlevel% neq 0 (
    echo [ERROR] APK rebuild failed!
    echo Trying alternative method...
    
    REM Try with different parameters
    apktool b apk_build\com.bilibili.AzurLane --use-aapt2 -o com.bilibili.azurlane-fixed.apk
    
    if %errorlevel% neq 0 (
        echo [ERROR] Alternative rebuild method also failed!
        echo Please check apktool installation
        pause
        exit /b 1
    )
)

echo [STEP 3] Signing the APK...
echo Signing APK with debug key...

REM Sign the APK
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore debug.keystore -storepass android -keypass android com.bilibili.azurlane-fixed.apk androiddebugkey

if %errorlevel% neq 0 (
    echo [WARNING] APK signing failed, but APK may still work
)

echo [STEP 4] Optimizing APK...
zipalign -v 4 com.bilibili.azurlane-fixed.apk com.bilibili.azurlane-webview-fixed.apk

if %errorlevel% neq 0 (
    echo [WARNING] APK optimization failed, using unoptimized version
    copy com.bilibili.azurlane-fixed.apk com.bilibili.azurlane-webview-fixed.apk
)

echo [STEP 5] Installing fixed APK...
echo Uninstalling old version...
adb uninstall com.bilibili.azurlane

echo Installing WebView-fixed version...
adb install com.bilibili.azurlane-webview-fixed.apk

if %errorlevel% eq 0 (
    echo [SUCCESS] WebView-fixed APK installed successfully!
) else (
    echo [ERROR] APK installation failed!
    echo You may need to install manually
)

echo.
echo ========================================
echo WebView Fix Rebuild Completed!
echo ========================================
echo.
echo Fixed APK: com.bilibili.azurlane-webview-fixed.apk
echo.
echo The WebView crash fix has been applied:
echo - Null URL checks added to prevent crashes
echo - Agreement page loading issues resolved
echo - App should now start without crashing
echo.
echo Please test the app now!

pause
