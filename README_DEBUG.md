# MuMu模拟器ADB调试指南

## 🚀 快速开始

### 1. 环境准备
确保以下工具已正确安装：
- MuMu模拟器
- Android SDK (包含adb, zipalign, apksigner)
- Java JDK
- Python (支持f-string)

### 2. 连接MuMu模拟器
```bash
# 启动MuMu模拟器后执行
adb connect 127.0.0.1:7555
adb devices
```

### 3. 使用调试脚本

#### 🔧 环境检查
```bash
debug_mumu.bat
```
用于检查ADB连接和监控应用日志

#### 📱 APK安装测试
```bash
install_test.bat
```
自动安装并启动APK，支持选择B站版本或英文版本

#### 🐛 闪退分析
```bash
crash_analysis.bat
```
收集系统信息并监控闪退日志，自动保存到文件

## 📋 手动调试步骤

### 检查APK完整性
```bash
# 检查APK签名
jarsigner -verify -verbose -certs apk_build\com.bilibili.AzurLane-.patched.apk

# 检查APK内容
aapt dump badging apk_build\com.bilibili.AzurLane-.patched.apk
```

### 监控特定错误
```bash
# 监控Java异常
adb logcat | findstr "AndroidRuntime"

# 监控Native崩溃
adb logcat | findstr "DEBUG.*tombstone"

# 监控内存问题
adb logcat | findstr "OutOfMemoryError"
```

### 应用状态检查
```bash
# 检查应用进程
adb shell ps | findstr azur

# 检查应用权限
adb shell dumpsys package com.bilibili.AzurLane | findstr permission

# 检查应用内存使用
adb shell dumpsys meminfo com.bilibili.AzurLane
```

## 🔍 常见问题排查

### 1. 签名问题
- 检查 `signing/testkey.pk8` 和 `signing/testkey.x509.pem` 文件
- 确认APK使用正确的签名密钥

### 2. 架构兼容性
- 检查 `lib/` 目录下的native库
- 确认支持模拟器架构 (通常是x86或x86_64)

### 3. 权限问题
- 检查AndroidManifest.xml中的权限声明
- 确认运行时权限已正确请求

### 4. 资源文件问题
- 检查assets目录完整性
- 验证资源文件路径和编码

## 📊 日志分析要点

### 关键错误标识
- `FATAL EXCEPTION`: Java层崩溃
- `signal 11 (SIGSEGV)`: Native层内存访问错误
- `OutOfMemoryError`: 内存不足
- `ClassNotFoundException`: 类加载失败
- `UnsatisfiedLinkError`: Native库加载失败

### 日志过滤技巧
```bash
# 只看错误级别日志
adb logcat *:E

# 看特定标签
adb logcat -s "ActivityManager:I" "System.err:W"

# 保存日志到文件
adb logcat > full_log.txt
```

## 🛠️ 修复验证流程

1. 根据日志确定问题类型
2. 修改相应的源码或配置
3. 重新构建APK: `python pieperseus.py`
4. 卸载旧版本并安装新版本
5. 重复测试直到问题解决

## 📞 获取帮助

如果遇到无法解决的问题，请提供：
- 完整的崩溃日志
- 设备和模拟器信息
- 复现步骤
- 使用的APK版本（B站版或英文版）
