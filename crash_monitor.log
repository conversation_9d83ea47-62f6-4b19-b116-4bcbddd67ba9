--------- beginning of main
08-29 20:51:55.429 D/SntpClient( 1351): request time failed: java.net.SocketTimeoutException: Poll timed out
08-29 20:51:56.377 D/ProcessState( 8376): Binder ioctl to enable oneway spam detection failed: Invalid argument
--------- beginning of system
08-29 20:51:56.379 I/ActivityManager( 1351): Force stopping com.bilibili.azurlane appid=10055 user=0: from pid 8376
08-29 20:51:56.380 I/ActivityManager( 1351): Killing 8239:com.bilibili.azurlane/u0a55 (adj 0): stop com.bilibili.azurlane due to from pid 8376
08-29 20:51:56.380 I/ActivityManager( 1351): Killing 8276:com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/u0a55i73 (adj 0): isolated not needed
08-29 20:51:56.381 W/ActivityTaskManager( 1351): Force removing ActivityRecord{524604a u0 com.bilibili.azurlane/com.base.gsc_agreement_library.AgreementWebActivity t3044 f}}: app died, no saved state
08-29 20:51:56.376 I/cmd     ( 8376): type=1400 audit(0.0:41800): avc: denied { sendto } for path="/dev/socket/logdw" scontext=u:r:adbd:s0 tcontext=u:r:init:s0 tclass=unix_dgram_socket permissive=1
08-29 20:51:56.386 W/InputManager-JNI( 1351): Input channel object '6a0a4c2 com.bilibili.azurlane/com.base.gsc_agreement_library.AgreementWebActivity (client)' was disposed without first being removed with the input manager!
08-29 20:51:56.387 D/RecentTaskMonitor( 1351): onRecentTaskRemoved 3044:Task=3044
08-29 20:51:56.387 E/RecentTaskMonitor( 1351): failed to call send close intent!
08-29 20:51:56.389 W/ActivityTaskManager( 1351): Can't find TaskDisplayArea to determine support for multi window. Task id=3044 attached=false
08-29 20:51:56.389 W/ActivityTaskManager( 1351): Can't find TaskDisplayArea to determine support for multi window. Task id=3044 attached=false
08-29 20:51:56.376 I/cmd     ( 8376): type=1400 audit(0.0:41801): avc: denied { call } for scontext=u:r:adbd:s0 tcontext=u:r:init:s0 tclass=binder permissive=1
08-29 20:51:56.393 D/RecentTaskMonitor( 1351): VAddressShellNotifier#notifyOpenTab :{"uri":"player\/tab","need_response":false,"params":{"id":"task:2","displayId":0,"userId":0,"name":"Mumu Launcher","originName":"Mumu Launcher","packageName":"com.mumu.launcher","launcher":true,"newTask":false,"icon":"iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAHOSURBVEiJ7ZTBatRAGMd\/mclacMCF7ta20pNPsFBEjQf7CB48CsUX8GDFd\/DiE3hp0ZsHMZrTQtiTgr34AuJhhT2IlsS1ktnk87BJmm2TdqnrrT8YSGa+\/L\/v+89M4IIF4+bjvxEDCXBl0cIuMASyfMSAWaS4aK0lCALp9\/vSbrcFkHyNENzwH6yLtdbi+76EYSgFxhght2sf4k+QfKyxTp1R+RAwvu9jjMHzvHJxNBoVMd8Oz2HXjC2DwUCstXKcJEmk0+kIIB8aOmii1pYmqnbNU\/kQyN69fyuPXizLr8ODUmhn92ptgjiOhYbT5RwTtwBBEGCM4eatGzx5uUIyGQPwfPsHl9w23b3PRDY7avdhjyUyut0uURQBtIAJzG7yz2pm785tnr5aK8UBHu8ul8+9a6tsbqyzubGOcqp1AvC7sKt6dtfyzF89z2srR3Pv+msm6eQUR2tZBcb5mEkwLt611mSS8ubLff7YCBHIMlAKtu6mtJSD64A6UXipU7Y91+3LUtjbgQfPoKUV37d7J2KsTWu\/Pe2iHQXpqbi7NE\/0LLUdWGvRriJLodgC5U47sdbWCjXN1yU8YPojO+9ovAcFlxvm52V8dsgFFf4Csc8O5d4NxXMAAAAASUVORK5CYII=","canChangeAppOrientation":false,"canReceiveFile":false,"pid":5877,"uid":1000,"action":"open"}}
08-29 20:51:56.393 D/RecentTaskMonitor( 1351): VAddressShellNotifier#notifyCloseTab :{"uri":"player\/tab","need_response":false,"params":{"id":"task:3044","action":"close"}}
08-29 20:51:56.376 I/servicemanager( 1104): type=1400 audit(0.0:41802): avc: denied { transfer } for scontext=u:r:init:s0 tcontext=u:r:adbd:s0 tclass=binder permissive=1
08-29 20:51:56.396 E/system_server( 1351): Cannot read thread CPU times for PID 1351
08-29 20:51:56.399 V/WindowManager( 1351): Unknown focus tokens, dropping reportFocusChanged
08-29 20:51:56.400 I/libprocessgroup( 1351): Successfully killed process cgroup uid 10055 pid 8239 in 20ms
08-29 20:51:56.376 I/cmd     ( 8376): type=1400 audit(0.0:41803): avc: denied { call } for scontext=u:r:adbd:s0 tcontext=u:r:system_server:s0 tclass=binder permissive=1
08-29 20:51:56.401 D/Launcher.Workspace( 5877): setInsets: insets = Rect(0, 36 - 0, 0)
08-29 20:51:56.402 W/ContextImpl( 5877): Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1181 android.content.ContextWrapper.sendBroadcast:479 com.mumu.launcher.Launcher.onResume:215 android.app.Instrumentation.callActivityOnResume:1492 android.app.Activity.performResume:8198 
08-29 20:51:56.404 D/ThrowableReceiver( 7736): intent.getAction() == Intent { act=android.intent.action.LAUNCHER_RESUME flg=0x10 pkg=com.mumu.store cmp=com.mumu.store/.install.ThrowableReceiver }
08-29 20:51:56.404 D/ThrowableReceiver( 7736): receive android.intent.action.LAUNCHER_RESUME
08-29 20:51:56.376 I/cmd     ( 8376): type=1400 audit(0.0:41804): avc: denied { transfer } for scontext=u:r:adbd:s0 tcontext=u:r:system_server:s0 tclass=binder permissive=1
08-29 20:51:56.406 I/Zygote  ( 1867): Process 8276 exited due to signal 9 (Killed)
08-29 20:51:56.410 E/system_server( 1351): Cannot read thread CPU times for PID 1351
08-29 20:51:56.412 V/ActivityManager( 1351): Got obituary of 8239:com.bilibili.azurlane
08-29 20:51:56.412 D/ConnectivityService( 1351): ConnectivityService NetworkRequestInfo binderDied(uid/pid:10055/8239, android.os.BinderProxy@15332e)
08-29 20:51:56.412 D/ConnectivityService( 1351): ConnectivityService NetworkRequestInfo binderDied(uid/pid:10055/8239, android.os.BinderProxy@232fecf)
08-29 20:51:56.413 D/ConnectivityService( 1351): releasing NetworkRequest [ REQUEST id=465, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10055 RequestorUid: 10055 RequestorPkg: com.bilibili.azurlane] ] (release request)
08-29 20:51:56.415 W/ActivityManager( 1351): setHasOverlayUi called on unknown pid: 8239
08-29 20:51:56.418 I/Zygote  ( 1182): Process 8239 exited due to signal 9 (Killed)
08-29 20:51:56.421 I/libprocessgroup( 1351): Successfully killed process cgroup uid 99073 pid 8276 in 0ms
08-29 20:51:56.432 E/BpTransactionCompletedListener( 1205): Failed to transact (-32)
08-29 20:51:56.487 D/opengl-gc( 1236): Check pid 8239: running 0
08-29 20:51:56.535 D/AndroidRuntime( 8379): >>>>>> START com.android.internal.os.RuntimeInit uid 2000 <<<<<<
08-29 20:51:56.538 I/AndroidRuntime( 8379): Using default boot image
08-29 20:51:56.538 I/AndroidRuntime( 8379): Leaving lock profiling enabled
08-29 20:51:56.540 W/app_process( 8379): ART APEX data files are untrusted.
08-29 20:51:56.587 D/app_process( 8379): Time zone APEX ICU file found: /apex/com.android.tzdata/etc/icu/icu_tzdata.dat
08-29 20:51:56.587 D/app_process( 8379): I18n APEX ICU file found: /apex/com.android.i18n/etc/icu/icudt68l.dat
08-29 20:51:56.597 W/app_process( 8379): Unexpected CPU variant for X86 using defaults: x86_64
08-29 20:51:56.616 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.616 E/Zygote  ( 8379): The " /system/etc/mumu-configs/device-prop-configs/arm.config" app property config file not exists!
08-29 20:51:56.616 W/Zygote  ( 8379): In ".*shootgame.*" have not valid property config, ignore it
08-29 20:51:56.616 E/Zygote  ( 8379): The " /system/etc/mumu-configs/device-prop-configs/arm.config" app property config file not exists!
08-29 20:51:56.616 W/Zygote  ( 8379): In ".*hanju.*" have not valid property config, ignore it
08-29 20:51:56.617 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi6.config
08-29 20:51:56.617 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.617 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi6.config
08-29 20:51:56.618 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/p50pro.config
08-29 20:51:56.618 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/p50pro.config
08-29 20:51:56.618 I/Zygote  ( 8379): getAppDeviceConfig find player priority
08-29 20:51:56.618 I/Zygote  ( 8379): getAppDeviceConfig find player priority
08-29 20:51:56.618 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/samsungsmg9910.config
08-29 20:51:56.619 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/tzyxmznew.config
08-29 20:51:56.619 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/tzyxmznew.config
08-29 20:51:56.619 E/Zygote  ( 8379): The " /system/etc/mumu-configs/device-prop-configs/armv7.config" app property config file not exists!
08-29 20:51:56.619 W/Zygote  ( 8379): In "com\.sqw\.setdl\..*" have not valid property config, ignore it
08-29 20:51:56.619 E/Zygote  ( 8379): The " /system/etc/mumu-configs/device-prop-configs/armv7.config" app property config file not exists!
08-29 20:51:56.619 W/Zygote  ( 8379): In "com\.sqwtt\.setdl\.aer" have not valid property config, ignore it
08-29 20:51:56.619 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mate10pro.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/gjqtmyr.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mate10pro.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi6.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mate10pro.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mate10pro.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi11pro.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.620 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi11pro.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi11pro.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi11pro.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi11pro.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi11pro.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi11pro.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.621 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.622 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/mumu.config
08-29 20:51:56.624 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi14pro.config
08-29 20:51:56.624 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi14pro.config
08-29 20:51:56.624 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi14pro.config
08-29 20:51:56.624 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/overseas_sim_info.config
08-29 20:51:56.624 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/overseas_sim_info.config
08-29 20:51:56.625 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.625 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/x86.config
08-29 20:51:56.626 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/tzyxmznew.config
08-29 20:51:56.626 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.628 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/yyb.config
08-29 20:51:56.629 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/samsungsms9006.config
08-29 20:51:56.629 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/yyb.config
08-29 20:51:56.629 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.629 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/coc.config
08-29 20:51:56.629 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/samsung_v2q.config
08-29 20:51:56.629 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.629 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/overseas_sim_info.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/liapp.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/liapp.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/samsungsms9080.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi14pro.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/arm64.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/arm64.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.630 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/xiaomi8_arm.config
08-29 20:51:56.631 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/yyb.config
08-29 20:51:56.631 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/samsung_v2q.config
08-29 20:51:56.631 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/samsung_v2q.config
08-29 20:51:56.631 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/samsung_v2q.config
08-29 20:51:56.631 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/samsung_v2q.config
08-29 20:51:56.631 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/arm64.config
08-29 20:51:56.631 I/Zygote  ( 8379): config file alreay parse:/system/etc/mumu-configs/device-prop-configs/samsungsms9080.config
08-29 20:51:56.632 D/Zygote  ( 8379): Apps device config data:{com\..*\.gplay\.punishing\.grayraven.*={ro.product.model=MuMu, ro.product.manufacturer=Netease, ro.product.name=cancro, ro.product.board=hammerhead, ro.product.brand=Android, ro.product.device=cancro}, com\.tencent\.pwrd\.projectt={ro.product.model=M2102K1AC, ro.product.manufacturer=Xiaomi, ro.product.name=mars, ro.product.board=mars, ro.product.brand=Xiaomi, ro.product.device=mars}, com\.tencent\.mtt={ro.build.version.all_codenames=REL, ro.product.manufacturer=Xiaomi, ro.boot.ramdump=disable, ro.vendor.build.date=Sun, ro.build.hardware.version=V1, ro.hardware=qcom, ro.build.version.codename=REL, ro.build.version.release=12.0.0, ro.bootimage.build.fingerprint=Xiaomi/dipper/dipper:12.0.0/OPM1.171019.026/V10.0.11.0.OEACNFH:user/release-keys, ro.build.date.utc=1541867668, ro.bootimage.build.date.utc=1541867668, ro.product.cuptsm=XIAOMI|ESE|02|01, ro.vendor.extension_library=libqti-perfd-client.so, ro.product.cpu.abilist64=arm64-v8a, ro.product.name=dipper, ro.boot.serialno=e20b6590, ro.product.locale=zh-CN, ro.board.platform=sdm845, qcom.bluetooth.soc=cherokee, ro.boot.hardware=qcom, ro.build.user=builder, ro.boot.hwc=CN, ro.bootimage.build.date=Sun, ro.product.cpu.abi=arm64-v8a, ro.build.product=dipper, ro.build.type=user, ro.build.description=dipper-user, ro.vendor.product.manufacturer=Xiaomi, ro.vendor.product.model=MI, ro.product.cpu.abilist32=armeabi-v7a,armeabi, ro.build.version.sdk=31, ro.product.board=sdm845, ro.boot.verifiedbootstate=green, ro.build.version.incremental=V10.0.11.0.QQqun|*********, ro.build.version.security_patch=2018-10-01, ro.product.cpu.abilist=arm64-v8a,armeabi-v7a,armeabi, ro.boot.secureboot=1, ro.vendor.product.device=dipper, ro.boot.hwlevel=MP, ro.build.date=Sun, ro.build.host=c3-miui-ota-bd123.bj, ro.build.tags=release-keys, ro.boot.usbcontroller=a600000.dwc3, ro.vendor.build.fingerprint=Xiaomi/dipper/dipper:12.0.0/OPM1.171019.026/V10.0.11.0.OEACNFH:user/release-keys, ro.product.brand=Xiaomi, ro.boot.baseband=sdm, ro.boot.veritymode=enforcing, ro.build.software.version=Android12.0.0_10, ro.build.flavor=dipper-user, ro.build.characteristics=nosdcard, ro.vendor.build.date.utc=1541867668, ro.product.model=MI, ro.boot.keymaster=1, ro.vendor.product.name=dipper, ro.product.first_api_level=31, ro.boot.hwversion=3.3.0, ro.product.device=dipper, ro.baseband=sdm, ro.build.fingerprint=Xiaomi/dipper/dipper:12.0.0/OPM1.171019.026/V10.0.11.0.OEACNFH:user/release-keys, ro.vendor.product.brand=Xiaomi, ro.build.version.preview_sdk=0, ro.product.cert=M1803E1A}, com\.supercell\.clashroyale={persist.sys.chromeos_channel=stable}, com\.tencent\.nrc={ro.product.model=23116PN5BC, ro.product.manufacturer=Xiaomi, ro.product.name=shennong, ro.product.board=shennong, ro.product.brand=Xiaomi, ro.product.device=shennong}, com\.netease\.party.*={ro.product.model=MuMu, ro.product.manufacturer=Netease, ro.product.name=cancro, ro.product.board=hammerhead, ro.product.brand=Android, ro.product.device=cancro}, .*com\.riotgames\.league\.wildrift.*={ro.product.model=SM-G9910, ro.product.manufacturer=samsung, ro.product.name=SM-G9910, ro.product.board=SM-G9910, ro.product.brand=samsung, ro.product.device=SM-G9910}, .*com\.tencent\.tmgp\.sgame.*={ro.board.api_level=31, ro.vendor.build.version.release_or_codename=12, ro.boot.fstab_suffix=default, ro.build.version.all_codenames=REL, ro.product.system_ext.device=system_ext_qcom, ro.build.version.release_or_codename=12, ro.product.product.device=system_ext_qcom, ro.build.version.codename=REL, ro.radio.vsim_modem_count=2, ro.system.build.version.release_or_codename=12, ro.hw.custPath=/version/cust/all/cn, ro.product.locale.region=CN, ro.bootimage.build.fingerprint=Honor/system_ext_qcom/system_ext_qcom:12/SP1A.210812.016/root06071047:user/dev-keys, ro.product.bootimage.brand=Honor, ro.product.name=LGE-AN10, ro.product.locale=zh-Hans-CN, ro.build.hw_emui_api_level=33, ro.product.vendor.brand=qti, ro.property_service.version=2, ro.setupwizard.mode=DISABLED, ro.build.update_version=V1_2, ro.bootmode=unknown, ro.honor.build.host=c
08-29 20:51:56.632 D/Zygote  ( 8379): Apps device config player priority data: [.*dts\.freefire.*, .*com\.riotgames\.league\.wildrift.*]
08-29 20:51:56.632 E/Zygote  ( 8379): The " /pcgame/app-device-prop-overlay.config" property config file not exists!
08-29 20:51:56.633 D/Zygote  ( 8379): getAppDeviceChannelConfig pattern = .*\.mi
08-29 20:51:56.633 D/Zygote  ( 8379): getAppDeviceChannelConfig pattern = .*\.samsung
08-29 20:51:56.633 D/Zygote  ( 8379): getAppDeviceChannelConfig pattern = .*\.huawei
08-29 20:51:56.634 D/Zygote  ( 8379): Apps device channel config data:{.*\.mi={ro.product.model=M2102K1AC, ro.product.manufacturer=Xiaomi, ro.product.name=mars, ro.product.board=mars, ro.product.brand=Xiaomi, ro.product.device=mars}, .*\.huawei={ro.product.model=BLA-AL00, ro.product.manufacturer=HUAWEI, ro.product.name=BLA-AL00, ro.product.board=BLA-AL00, ro.product.brand=HUAWEI, ro.product.device=BLA-AL00}, .*\.samsung={ro.product.model=SM-G9910, ro.product.manufacturer=samsung, ro.product.name=SM-G9910, ro.product.board=SM-G9910, ro.product.brand=samsung, ro.product.device=SM-G9910}}
08-29 20:51:56.637 D/ProcessState( 8379): Binder ioctl to enable oneway spam detection failed: Invalid argument
08-29 20:51:56.637 D/AndroidRuntime( 8379): Calling main entry com.android.commands.monkey.Monkey
08-29 20:51:56.638 W/Monkey  ( 8379): args: [-p, com.bilibili.azurlane, -c, android.intent.category.LAUNCHER, 1]
08-29 20:51:56.639 W/Monkey  ( 8379):  arg: "-p"
08-29 20:51:56.639 W/Monkey  ( 8379):  arg: "com.bilibili.azurlane"
08-29 20:51:56.639 W/Monkey  ( 8379):  arg: "-c"
08-29 20:51:56.639 W/Monkey  ( 8379):  arg: "android.intent.category.LAUNCHER"
08-29 20:51:56.639 W/Monkey  ( 8379):  arg: "1"
08-29 20:51:56.639 W/Monkey  ( 8379): data="com.bilibili.azurlane"
08-29 20:51:56.639 W/Monkey  ( 8379): data="android.intent.category.LAUNCHER"
08-29 20:51:56.642 W/BroadcastQueue( 1351): Skipping deliver [background] BroadcastRecord{aa8648 u-1 android.net.conn.CONNECTIVITY_CHANGE} to ReceiverList{1354ce1 8379 (unknown name)/2000/u-1 remote:345543a}: process gone or crashing
08-29 20:51:56.644 I/ActivityTaskManager( 1351): START u0 {act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.bilibili.azurlane/com.manjuu.azurlane.MainActivity} from uid 2000, pid 8379
08-29 20:51:56.646 I/ActivityTaskManager( 1351): [KeepAlive] disabled for com.bilibili.azurlane
08-29 20:51:56.646 W/ActivityTaskManager( 1351): Can't find TaskDisplayArea to determine support for multi window. Task id=3045 attached=false
08-29 20:51:56.647 D/CompatibilityChangeReporter( 1351): Compat change id reported: 174042980; UID 10055; state: DISABLED
08-29 20:51:56.647 D/CompatibilityChangeReporter( 1351): Compat change id reported: 184838306; UID 10055; state: DISABLED
08-29 20:51:56.647 D/CompatibilityChangeReporter( 1351): Compat change id reported: 185004937; UID 10055; state: DISABLED
08-29 20:51:56.647 D/CompatibilityChangeReporter( 1351): Compat change id reported: 181136395; UID 10055; state: DISABLED
08-29 20:51:56.647 D/CompatibilityChangeReporter( 1351): Compat change id reported: 174042936; UID 10055; state: DISABLED
08-29 20:51:56.648 D/CompatibilityChangeReporter( 1351): Compat change id reported: 197654537; UID 10055; state: DISABLED
08-29 20:51:56.648 D/CompatibilityChangeReporter( 1351): Compat change id reported: 168419799; UID 10055; state: DISABLED
08-29 20:51:56.649 D/RecentTaskMonitor( 1351): onRecentTaskAdded 3045:Task=3045
08-29 20:51:56.650 I/Monkey  ( 8379): Events injected: 1
08-29 20:51:56.650 D/Launcher.DragController( 5877): cancelDrag: 
08-29 20:51:56.650 D/Launcher.DragController( 5877): endDrag: 
08-29 20:51:56.654 D/CompatibilityChangeReporter( 1351): Compat change id reported: 135634846; UID 10055; state: DISABLED
08-29 20:51:56.654 D/CompatibilityChangeReporter( 1351): Compat change id reported: 143937733; UID 10055; state: ENABLED
08-29 20:51:56.654 I/Monkey  ( 8379): ## Network stats: elapsed time=9ms (0ms mobile, 0ms wifi, 9ms not connected)
08-29 20:51:56.655 W/RecentTaskMonitor( 1351): null pid retry ...
08-29 20:51:56.655 E/RecentTaskMonitor( 1351): VAddressShellNotifier#notifyOpenTab null request
08-29 20:51:56.656 D/Zygote  ( 1182): Forked child process 8395
08-29 20:51:56.657 I/ActivityManager( 1351): Start proc 8395:com.bilibili.azurlane/u0a55 for pre-top-activity {com.bilibili.azurlane/com.manjuu.azurlane.MainActivity}
08-29 20:51:56.657 I/app_process( 8379): System.exit called, status: 0
08-29 20:51:56.657 I/AndroidRuntime( 8379): VM exiting with result code 0.
08-29 20:51:56.658 I/Zygote  ( 8395): fakeProductInfoIfNeed callded! com.bilibili.azurlane
08-29 20:51:56.659 I/Zygote  ( 8395): fake Product info com.bilibili.azurlane not matched any pattern
08-29 20:51:56.659 I/Zygote  ( 8395): fakeProductInfoFromProperties callded! com.bilibili.azurlane
08-29 20:51:56.659 E/NemuAppNameUtil( 8395): save data success
08-29 20:51:56.661 W/nativebridge( 8395): Failed to bind-mount /system/etc/cpuinfo.x86.txt as /proc/cpuinfo: No such file or directory
08-29 20:51:56.661 W/nativebridge( 8395): Failed to bind-mount /system/lib/x86/cpuinfo as /proc/cpuinfo: No such file or directory
08-29 20:51:56.661 I/Zygote  ( 8395): seccomp disabled by setenforce 0
08-29 20:51:56.665 I/BpBinder( 1205): onLastStrongRef automatically unlinking death recipients: <uncached descriptor>
08-29 20:51:56.665 W/libili.azurlan( 8395): Unexpected CPU variant for X86 using defaults: x86_64
08-29 20:51:56.666 V/libnb   ( 8395): enter native_bridge2_initialize /data/user/0/com.bilibili.azurlane/code_cache arm
08-29 20:51:56.666 E/libnb   ( 8395): The translator implementation library has not been set.
08-29 20:51:56.667 D/gralloc_x86( 1539): gralloc_alloc: Creating ashmem region of size 3690496
08-29 20:51:56.671 D/ndroid.systemu( 1539): Features: 0 0 0
08-29 20:51:56.671 E/ndroid.systemu( 1539): vulkan version is 4206872
08-29 20:51:56.671 D/gralloc_x86( 1539): gralloc_alloc: Creating ashmem region of size 3690496
08-29 20:51:56.672 D/gralloc_x86( 1539): gralloc_alloc: Creating ashmem region of size 3690496
08-29 20:51:56.672 E/libnb   ( 8395): get_callbacks native_handle 0x53add067: 1 libnb:/system/lib/libhoudini.so
08-29 20:51:56.672 I/libnb   ( 8395): Found /system/lib/libhoudini.so version 6
08-29 20:51:56.672 D/HP      ( 8395): initialize private_dir:/data/user/0/com.bilibili.azurlane/code_cache instruction_set:arm
08-29 20:51:56.672 D/HP      ( 8395): patch_linker_namespace_32
08-29 20:51:56.672 D/HP      ( 8395): patch call constructors entrance 32
08-29 20:51:56.672 D/houdini ( 8395): [8395] Initialize library(version: 12.0.0_y.38816.m RELEASE)... successfully.
08-29 20:51:56.673 E/libili.azurlan( 8395): Not starting debugger since process cannot load the jdwp agent.
08-29 20:51:56.677 D/ProcessState( 8395): Binder ioctl to enable oneway spam detection failed: Invalid argument
08-29 20:51:56.682 W/OomAdjuster( 1351): Fallback pre-set sched group to default: not expected top priority
08-29 20:51:56.685 E/ashmem  ( 8395): memfd: ro.vndk.version not defined or invalid (), this is mandated since P.
08-29 20:51:56.685 D/CompatibilityChangeReporter( 8395): Compat change id reported: 171979766; UID 10055; state: DISABLED
08-29 20:51:56.697 D/NemuJavaHotPatchHelper( 8395): onHandleBindApplication reportedPackageName:com.bilibili.azurlane isMatchJavaPatch:false
08-29 20:51:56.707 I/Typeface( 8395): Preloading /system/fonts/Roboto-Regular.ttf
08-29 20:51:56.743 V/GraphicsEnvironment( 8395): ANGLE Developer option for 'com.bilibili.azurlane' set to: 'default'
08-29 20:51:56.743 V/GraphicsEnvironment( 8395): ANGLE GameManagerService for com.bilibili.azurlane: false
08-29 20:51:56.743 V/GraphicsEnvironment( 8395): Neither updatable production driver nor prerelease driver is supported.
08-29 20:51:56.745 D/NetworkSecurityConfig( 8395): No Network Security Config specified, using platform default
08-29 20:51:56.745 D/NetworkSecurityConfig( 8395): No Network Security Config specified, using platform default
08-29 20:51:56.746 I/MultiDex( 8395): VM with version 2.1.0 has multidex support
08-29 20:51:56.746 I/MultiDex( 8395): Installing application
08-29 20:51:56.746 I/MultiDex( 8395): VM has multidex support, MultiDex support library is disabled.
08-29 20:51:56.749 D/WM-WrkMgrInitializer( 8395): Initializing WorkManager with default configuration.
08-29 20:51:56.756 D/RecentTaskMonitor( 1351): task null or not sub display in notifyDisplayOpened
08-29 20:51:56.756 D/RecentTaskMonitor( 1351): VAddressShellNotifier#notifyOpenTab :{"uri":"player\/tab","need_response":false,"params":{"id":"task:3045","displayId":0,"userId":0,"name":"碧蓝航线","originName":"碧蓝航线","packageName":"com.bilibili.azurlane","launcher":false,"newTask":true,"icon":"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","canChangeAppOrientation":false,"canReceiveFile":false,"pid":8395,"uid":10055,"action":"open"}}
08-29 20:51:56.758 D/BgcPlugin( 8395): tag: LocalContentProvider ,msg: onCreate, current thread:main
08-29 20:51:56.768 D/CompatibilityChangeReporter( 8395): Compat change id reported: 160794467; UID 10055; state: DISABLED
08-29 20:51:56.772 E/ziparchive( 8395): Zip: lseek on fd -2 failed: Bad file descriptor
08-29 20:51:56.774 E/libili.azurlan( 8395): Invalid ID 0x00000000.
08-29 20:51:56.804 D/CompatibilityChangeReporter( 8395): Compat change id reported: 147798919; UID 10055; state: ENABLED
08-29 20:51:56.806 V/Perseus ( 8395): config loaded
08-29 20:51:56.808 I/unity   ( 8395): Graphics API pick the Graphics API based on PlayerSettings
08-29 20:51:56.817 D/gralloc_x86( 1539): gralloc_alloc: Creating ashmem region of size 176128
08-29 20:51:56.820 D/ndroid.systemu( 1539): Features: 0 0 0
08-29 20:51:56.820 E/ndroid.systemu( 1539): vulkan version is 4206872
08-29 20:51:56.820 D/gralloc_x86( 1539): gralloc_alloc: Creating ashmem region of size 176128
08-29 20:51:56.820 D/gralloc_x86( 1539): gralloc_alloc: Creating ashmem region of size 176128
08-29 20:51:56.823 I/IL2CPP  ( 8395): JNI_OnLoad
08-29 20:51:57.376 I/ps      ( 8431): type=1400 audit(0.0:41969): avc: denied { read } for name="pid_max" dev="proc" ino=26131 scontext=u:r:adbd:s0 tcontext=u:object_r:proc_pid_max:s0 tclass=file permissive=1
08-29 20:51:57.376 I/ps      ( 8431): type=1400 audit(0.0:41970): avc: denied { open } for path="/proc/sys/kernel/pid_max" dev="proc" ino=26131 scontext=u:r:adbd:s0 tcontext=u:object_r:proc_pid_max:s0 tclass=file permissive=1
08-29 20:51:57.383 I/ps      ( 8431): type=1400 audit(0.0:41971): avc: denied { getattr } for path="/proc/1091" dev="proc" ino=15894 scontext=u:r:adbd:s0 tcontext=u:r:ueventd:s0 tclass=dir permissive=1
08-29 20:51:57.383 I/ps      ( 8431): type=1400 audit(0.0:41972): avc: denied { search } for name="1091" dev="proc" ino=15894 scontext=u:r:adbd:s0 tcontext=u:r:ueventd:s0 tclass=dir permissive=1
08-29 20:51:57.383 I/ps      ( 8431): type=1400 audit(0.0:41973): avc: denied { read } for name="stat" dev="proc" ino=17864 scontext=u:r:adbd:s0 tcontext=u:r:ueventd:s0 tclass=file permissive=1
08-29 20:51:57.841 V/unity   ( 8395): set Cpu Index 7
08-29 20:51:57.842 I/unity   ( 8395): set affinity to 7 success
08-29 20:51:57.842 I/GSCPubCommon( 8395): init
08-29 20:51:57.847 E/DeviceUtil( 8395): DeviceUtil has been initialized
08-29 20:51:57.852 I/GSCPubCommon( 8395): initInternal
08-29 20:51:57.862 I/WebViewFactory( 8395): Loading com.android.webview version 101.0.4951.61 (code 495156106)
08-29 20:51:57.865 E/WebViewLibraryLoader( 8395): can't load with relro file; address space not reserved
08-29 20:51:57.870 I/cr_WVCFactoryProvider( 8395): Loaded version=101.0.4951.61 minSdkVersion=23 isBundle=false multiprocess=true packageId=2
08-29 20:51:57.884 I/cr_LibraryLoader( 8395): Successfully loaded native library
08-29 20:51:57.885 I/cr_CachingUmaRecorder( 8395): Flushed 9 samples from 9 histograms.
08-29 20:51:57.894 W/ActivityManager( 1351): callingPackage:com.bilibili.azurlane, packageName:com.android.webview, name:org.chromium.content.app.SandboxedProcessService0, isSkip: true
08-29 20:51:57.895 D/CompatibilityChangeReporter( 1351): Compat change id reported: 135634846; UID 10055; state: DISABLED
08-29 20:51:57.895 D/CompatibilityChangeReporter( 1351): Compat change id reported: 143937733; UID 10055; state: ENABLED
08-29 20:51:57.896 W/ActivityManager( 1351): callingPackage:com.bilibili.azurlane, packageName:com.android.webview, name:org.chromium.content.app.SandboxedProcessService0, isSkip: true
08-29 20:51:57.898 D/Zygote  ( 1867): Forked child process 8458
08-29 20:51:57.899 I/Zygote  ( 8458): fakeProductInfoIfNeed callded! com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0
08-29 20:51:57.900 I/Zygote  ( 8458): fake Product info com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0 not matched any pattern
08-29 20:51:57.900 I/Zygote  ( 8458): fakeProductInfoFromProperties callded! com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0
08-29 20:51:57.900 I/Zygote  ( 8458): fakeProductInfoFromProperties disable for uid ! com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0
08-29 20:51:57.901 I/ActivityManager( 1351): Start proc 8458:com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/u0i74 for  {com.bilibili.azurlane/org.chromium.content.app.SandboxedProcessService0:0}
08-29 20:51:57.903 E/NemuAppNameUtil( 8458): save data to file error
08-29 20:51:57.903 E/NemuAppNameUtil( 8458): save data fail, try save again!!
08-29 20:51:57.904 I/Zygote  ( 8458): seccomp disabled by setenforce 0
08-29 20:51:57.906 W/ocessService0:( 8458): Unexpected CPU variant for X86 using defaults: x86_64
08-29 20:51:57.908 E/ocessService0:( 8458): Not starting debugger since process cannot load the jdwp agent.
08-29 20:51:57.910 D/ProcessState( 8458): Binder ioctl to enable oneway spam detection failed: Invalid argument
08-29 20:51:57.916 E/ashmem  ( 8458): memfd: ro.vndk.version not defined or invalid (), this is mandated since P.
08-29 20:51:57.917 D/CompatibilityChangeReporter( 8458): Compat change id reported: 171979766; UID 99074; state: ENABLED
08-29 20:51:57.925 W/ActivityManager( 1351): callingPackage:com.bilibili.azurlane, packageName:com.android.webview, name:org.chromium.android_webview.services.ComponentsProviderService, isSkip: true
08-29 20:51:57.926 W/ActivityManager( 1351): callingPackage:com.bilibili.azurlane, packageName:com.android.webview, name:org.chromium.android_webview.services.MetricsUploadService, isSkip: true
08-29 20:51:57.929 D/NemuJavaHotPatchHelper( 8458): onHandleBindApplication reportedPackageName:com.bilibili.azurlane isMatchJavaPatch:false
08-29 20:51:57.929 I/Typeface( 8458): Preloading /system/fonts/Roboto-Regular.ttf
08-29 20:51:57.936 D/CompatibilityChangeReporter( 8395): Compat change id reported: 171228096; UID 10055; state: ENABLED
08-29 20:51:57.940 D/ConnectivityService( 1351): requestNetwork for uid/pid:10055/8395 activeRequest: null callbackRequest: 467 [NetworkRequest [ REQUEST id=468, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10055 RequestorUid: 10055 RequestorPkg: com.bilibili.azurlane] ]] callback flags: 0 priority: **********
08-29 20:51:57.940 D/ConnectivityService( 1351): NetReassign [468 : null → 100]
08-29 20:51:57.940 D/WifiNetworkFactory( 1351): got request NetworkRequest [ REQUEST id=468, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10055 RequestorUid: 10055 RequestorPkg: com.bilibili.azurlane] ]
08-29 20:51:57.941 D/UntrustedWifiNetworkFactory( 1351): got request NetworkRequest [ REQUEST id=468, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10055 RequestorUid: 10055 RequestorPkg: com.bilibili.azurlane] ]
08-29 20:51:57.941 D/OemPaidWifiNetworkFactory( 1351): got request NetworkRequest [ REQUEST id=468, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10055 RequestorUid: 10055 RequestorPkg: com.bilibili.azurlane] ]
08-29 20:51:57.942 D/ConnectivityService( 1351): NetReassign [no changes]
08-29 20:51:57.956 W/System  ( 1351): A resource failed to call release. 
08-29 20:51:57.956 W/System  ( 1351): A resource failed to call release. 
08-29 20:51:57.957 D/NetworkSecurityConfig( 8458): No Network Security Config specified, using platform default
08-29 20:51:57.957 D/NetworkSecurityConfig( 8458): No Network Security Config specified, using platform default
08-29 20:51:57.958 I/cr_WebViewApkApp( 8458): Launched version=101.0.4951.61 minSdkVersion=23 isBundle=false processName=com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0
08-29 20:51:57.959 I/cr_ChildProcessService( 8458): Creating new ChildProcessService pid=8458
08-29 20:51:57.961 E/WebViewLibraryLoader( 8458): can't load with relro file; address space not reserved
08-29 20:51:57.966 D/WTF     ( 8395): ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
08-29 20:51:57.967 D/WTF     ( 8395): │ GSCPubCommon.a  (GSCPubCommon.java:221)
08-29 20:51:57.967 D/WTF     ( 8395): │    GSCPubCommon.initInternal  (GSCPubCommon.java:6)
08-29 20:51:57.967 D/WTF     ( 8395): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
08-29 20:51:57.967 D/WTF     ( 8395): │ RouteProcessService /init/agreement subscribe
08-29 20:51:57.967 D/WTF     ( 8395): └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
08-29 20:51:57.974 I/GSCPubCommon( 8395): aOl
08-29 20:51:57.977 W/WindowManager( 1351): Failed looking up window session=Session{82e05 8395:u0a10055} callers=com.android.server.wm.WindowManagerService.windowForClientLocked:5827 com.android.server.wm.Session.updateRequestedVisibilities:640 android.view.IWindowSession$Stub.onTransact:1175 
08-29 20:51:57.995 I/RecentTaskMonitor( 1351): broadcast tab opened in android as it's new task1 3045
08-29 20:51:57.995 D/TabManager( 1501): setFocusTaskProp 3045
08-29 20:51:57.995 W/BroadcastQueue( 1351): Background execution not allowed: receiving Intent { act=nemu.intent.action.NEW_TASK dat=package:com.bilibili.azurlane flg=0x10 (has extras) } to com.mumu.acc/.AccMsgReceiver
08-29 20:51:57.995 D/create_screen( 5877): onReceive: action = nemu.intent.action.NEW_TASK
08-29 20:51:57.996 W/ContextImpl( 1501): Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1181 android.content.ContextWrapper.sendBroadcast:479 com.netease.nemu_vapi_android.thread.NewFileUpdater.notifyFocusedTabChanged:494 com.netease.nemu_vapi_android.thread.NewFileUpdater.onOpenNewTask:410 com.netease.nemu_vapi_android.worker.LocalAppManager.updateOnNewTask:255 
08-29 20:51:57.996 D/NewFileUpdater( 1501): onOpenNewTask focusPkgName set to com.bilibili.azurlane
08-29 20:51:57.997 D/updatePackage( 5877): version com.bilibili.azurlane 9611
08-29 20:51:57.997 I/Launcher( 5877): Deferring update until onResume
08-29 20:51:57.997 W/ActivityManager( 1351): Foreground service started from background can not have location/camera/microphone access: service com.mumu.acc/.AccService
08-29 20:51:57.997 V/ActivityManager( 1351): Attempted to start a foreground service (com.mumu.acc/.AccService) with a broken notification (no icon: Notification(channel=acc-service shortcut=null contentView=null vibrate=null sound=null defaults=0x0 flags=0x40 color=0x00000000 vis=PRIVATE))
08-29 20:51:57.997 D/Watchdog( 1501): upload event: {"architecture":"x86_64","channel":"gw-overseas12","country":"zh-CN","engine":"NEMUX","fchannel":"nochannel-mumu12","language":"zh-Hans","mpid":"0","package":"mumu","product":"","product_version":"4.1.25.3699","usage":"0","uuid":"65463e8a-eca2-4c79-8ffc-47a101a05fa7","version":"4.1.25.3699","x":{"app_name":"碧蓝航线","app_package":"com.bilibili.azurlane","app_version_code":9611,"app_version_name":"9.6.11","error":{"code":0,"msg":"ok"},"extra":{"NB":"{\"NB_MD5\":\"4D2A0CEC4773598D1564C27B158959EA\",\"NB_NAME\":\"libhoudini.so\",\"NB_64BIT\":\"false\",\"NB_STATUS\":\"1\"}","amp_name":"碧蓝航线","ampid":0},"token":"a439e25f-16a8-4028-8a42-aa74c330c0b1"}}	type: LaunchAPP	sign: VXFy3nTSONx/JU2kW+hZfzUSaSc=	uid: 1000	json:{"app_name":"碧蓝航线","app_package":"com.bilibili.azurlane","app_version_code":9611,"app_version_name":"9.6.11","error":{"code":0,"msg":"ok"},"extra":{"NB":"{\"NB_MD5\":\"4D2A0CEC4773598D1564C27B158959EA\",\"NB_NAME\":\"libhoudini.so\",\"NB_64BIT\":\"false\",\"NB_STATUS\":\"1\"}","amp_name":"碧蓝航线","ampid":0},"token":"a439e25f-16a8-4028-8a42-aa74c330c0b1"}
08-29 20:51:57.998 W/ConnectivityManager.CallbackHandler( 8395): callback not found for CALLBACK_AVAILABLE message
08-29 20:51:57.998 W/ConnectivityManager.CallbackHandler( 8395): callback not found for CALLBACK_AVAILABLE message
08-29 20:51:58.006 D/hw-ProcessState( 8395): Binder ioctl to enable oneway spam detection failed: Invalid argument
08-29 20:51:58.007 I/hwservicemanager( 1106): getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
08-29 20:51:58.007 E/HidlServiceManagement( 8395): getService: Potential race detected, descriptor: android.hardware.graphics.mapper@4.0::IMapper instance: default
08-29 20:51:58.007 I/hwservicemanager( 1106): Since android.hardware.graphics.mapper@4.0::IMapper/default is not registered, trying to start it as a lazy HAL.
08-29 20:51:58.007 I/Gralloc4( 8395): mapper 4.x is not supported
08-29 20:51:58.007 I/hwservicemanager( 1106): getTransport: Cannot find entry android.hardware.graphics.mapper@3.0::IMapper/default in either framework or device VINTF manifest.
08-29 20:51:58.007 E/HidlServiceManagement( 8395): getService: Potential race detected, descriptor: android.hardware.graphics.mapper@3.0::IMapper instance: default
08-29 20:51:58.007 I/hwservicemanager( 1106): Since android.hardware.graphics.mapper@3.0::IMapper/default is not registered, trying to start it as a lazy HAL.
08-29 20:51:58.007 W/Gralloc3( 8395): mapper 3.x is not supported
08-29 20:51:58.007 E/HidlServiceManagement( 8395): getService: Potential race detected, descriptor: android.hardware.graphics.mapper@2.0::IMapper instance: default
08-29 20:51:58.011 W/libc    ( 1106): Unable to set property "ctl.interface_start" to "android.hardware.graphics.mapper@4.0::IMapper/default": error code: 0x20
08-29 20:51:58.011 I/hwservicemanager( 1106): Tried to start android.hardware.graphics.mapper@4.0::IMapper/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
08-29 20:51:58.016 W/libc    ( 1106): Unable to set property "ctl.interface_start" to "android.hardware.graphics.mapper@3.0::IMapper/default": error code: 0x20
08-29 20:51:58.016 I/hwservicemanager( 1106): Tried to start android.hardware.graphics.mapper@3.0::IMapper/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
08-29 20:51:58.017 D/Watchdog( 1501): upload server response: {"errcode": 100, "errmsg": "ok"}
08-29 20:51:58.017 D/Watchdog( 1501): upload result: true
08-29 20:51:58.017 W/NewFileUpdater( 1501): MSG_GET_PACKAGE_TAG_INFO, no available new tag for: com.bilibili.azurlane, workingTag: 12.1395
08-29 20:51:58.031 I/hwservicemanager( 1106): getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
08-29 20:51:58.031 E/HidlServiceManagement( 8395): getService: Potential race detected, descriptor: android.hardware.graphics.allocator@4.0::IAllocator instance: default
08-29 20:51:58.031 I/hwservicemanager( 1106): Since android.hardware.graphics.allocator@4.0::IAllocator/default is not registered, trying to start it as a lazy HAL.
08-29 20:51:58.031 W/Gralloc4( 8395): allocator 4.x is not supported
08-29 20:51:58.032 I/hwservicemanager( 1106): getTransport: Cannot find entry android.hardware.graphics.allocator@3.0::IAllocator/default in either framework or device VINTF manifest.
08-29 20:51:58.032 E/HidlServiceManagement( 8395): getService: Potential race detected, descriptor: android.hardware.graphics.allocator@3.0::IAllocator instance: default
08-29 20:51:58.032 I/hwservicemanager( 1106): Since android.hardware.graphics.allocator@3.0::IAllocator/default is not registered, trying to start it as a lazy HAL.
08-29 20:51:58.032 W/Gralloc3( 8395): allocator 3.x is not supported
08-29 20:51:58.032 E/HidlServiceManagement( 8395): getService: Potential race detected, descriptor: android.hardware.graphics.allocator@2.0::IAllocator instance: default
08-29 20:51:58.035 D/libili.azurlan( 8395): Features: 0 0 0
08-29 20:51:58.035 E/libili.azurlan( 8395): vulkan version is 4206872
08-29 20:51:58.035 D/gralloc_x86( 8395): gralloc_alloc: Creating ashmem region of size 3690496
08-29 20:51:58.037 W/libc    ( 1106): Unable to set property "ctl.interface_start" to "android.hardware.graphics.allocator@4.0::IAllocator/default": error code: 0x20
08-29 20:51:58.037 I/hwservicemanager( 1106): Tried to start android.hardware.graphics.allocator@4.0::IAllocator/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
08-29 20:51:58.041 W/libc    ( 1106): Unable to set property "ctl.interface_start" to "android.hardware.graphics.allocator@3.0::IAllocator/default": error code: 0x20
08-29 20:51:58.041 I/hwservicemanager( 1106): Tried to start android.hardware.graphics.allocator@3.0::IAllocator/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
08-29 20:51:58.052 I/ActivityTaskManager( 1351): Displayed com.bilibili.azurlane/com.manjuu.azurlane.MainActivity: +1s404ms
08-29 20:51:58.068 I/Unity   ( 8395): MemoryManager: Using 'Dynamic Heap' Allocator.
08-29 20:51:58.204 W/InputManager-JNI( 1351): Input channel object '97e3092 Splash Screen com.bilibili.azurlane (client)' was disposed without first being removed with the input manager!
08-29 20:51:58.379 I/ps      ( 8507): type=1400 audit(0.0:42077): avc: denied { getattr } for path="/proc/1231" dev="proc" ino=17438 scontext=u:r:adbd:s0 tcontext=u:r:su:s0 tclass=dir permissive=1
08-29 20:51:58.379 I/ps      ( 8507): type=1400 audit(0.0:42078): avc: denied { search } for name="1231" dev="proc" ino=17438 scontext=u:r:adbd:s0 tcontext=u:r:su:s0 tclass=dir permissive=1
08-29 20:51:58.379 I/ps      ( 8507): type=1400 audit(0.0:42079): avc: denied { read } for name="stat" dev="proc" ino=17914 scontext=u:r:adbd:s0 tcontext=u:r:su:s0 tclass=file permissive=1
08-29 20:51:58.379 I/ps      ( 8507): type=1400 audit(0.0:42080): avc: denied { open } for path="/proc/1231/stat" dev="proc" ino=17914 scontext=u:r:adbd:s0 tcontext=u:r:su:s0 tclass=file permissive=1
08-29 20:51:58.379 I/ps      ( 8507): type=1400 audit(0.0:42081): avc: denied { getattr } for path="/proc/1351" dev="proc" ino=2976 scontext=u:r:adbd:s0 tcontext=u:r:system_server:s0 tclass=dir permissive=1
08-29 20:51:59.383 W/BestClock( 1351): java.time.DateTimeException: Missing NTP fix
08-29 20:51:59.406 I/NetworkStats( 1351): type=1400 audit(0.0:42124): avc: denied { map_read } for scontext=u:r:system_server:s0 tcontext=u:r:init:s0 tclass=bpf permissive=1
08-29 20:51:59.406 I/NetworkStats( 1351): type=1400 audit(0.0:42125): avc: denied { map_write } for scontext=u:r:system_server:s0 tcontext=u:r:init:s0 tclass=bpf permissive=1
08-29 20:51:59.409 I/iptables-restor( 1207): type=1400 audit(0.0:42126): avc: denied { create } for scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=rawip_socket permissive=1
08-29 20:51:59.409 I/iptables-restor( 1207): type=1400 audit(0.0:42127): avc: denied { getopt } for lport=255 scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=rawip_socket permissive=1
08-29 20:51:59.418 W/BestClock( 1351): java.time.DateTimeException: Missing NTP fix
08-29 20:51:59.420 W/BestClock( 1351): java.time.DateTimeException: Missing NTP fix
08-29 20:51:59.421 W/BestClock( 1351): java.time.DateTimeException: Missing NTP fix
08-29 20:51:59.424 E/TelephonyManager( 1351): Error calling setDataEnabledForReason e:java.lang.IllegalStateException: telephony service is null.
08-29 20:51:59.493 I/wifi@1.0-servic( 1202): type=1400 audit(0.0:42128): avc: denied { write } for scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_route_socket permissive=1
08-29 20:51:59.991 I/Unity   ( 8395): SystemInfo CPU = x86-64 SSE3, Cores = 8, Memory = 5949mb
08-29 20:51:59.991 I/Unity   ( 8395): ApplicationInfo com.bilibili.azurlane version 9.6.11
08-29 20:51:59.991 I/Unity   ( 8395): Built from '2022.3/staging' branch, Version '2022.3.51f1 (9f9d16c45e54)', Build type 'Release', Scripting Backend 'il2cpp', CPU 'x86', Stripping 'Disabled'
08-29 20:52:00.254 I/Zygote  ( 1182): Process 8395 exited due to signal 11 (Segmentation fault)
08-29 20:52:00.262 I/WindowManager( 1351): WIN DEATH: Window{c0ff88b u0 com.bilibili.azurlane/com.manjuu.azurlane.MainActivity}
08-29 20:52:00.263 I/libprocessgroup( 1351): Successfully killed process cgroup uid 10055 pid 8395 in 0ms
08-29 20:52:00.264 W/InputManager-JNI( 1351): Input channel object 'c0ff88b com.bilibili.azurlane/com.manjuu.azurlane.MainActivity (client)' was disposed without first being removed with the input manager!
08-29 20:52:00.264 I/ActivityManager( 1351): Process com.bilibili.azurlane (pid 8395) has died: fg  TOP 
08-29 20:52:00.272 I/ActivityManager( 1351): Killing 8458:com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/u0a55i74 (adj 0): isolated not needed
08-29 20:52:00.277 I/Zygote  ( 1867): Process 8458 exited due to signal 9 (Killed)
08-29 20:52:00.277 W/ActivityTaskManager( 1351): Force removing ActivityRecord{b59f8c7 u0 com.bilibili.azurlane/com.manjuu.azurlane.MainActivity t3045}: app died, no saved state
08-29 20:52:00.278 I/libprocessgroup( 1351): Successfully killed process cgroup uid 99074 pid 8458 in 5ms
08-29 20:52:00.279 D/RecentTaskMonitor( 1351): onRecentTaskRemoved 3045:Task=3045
08-29 20:52:00.279 E/RecentTaskMonitor( 1351): failed to call send close intent!
08-29 20:52:00.284 E/BpTransactionCompletedListener( 1205): Failed to transact (-32)
08-29 20:52:00.285 W/ActivityTaskManager( 1351): Can't find TaskDisplayArea to determine support for multi window. Task id=3045 attached=false
08-29 20:52:00.285 W/ActivityTaskManager( 1351): Can't find TaskDisplayArea to determine support for multi window. Task id=3045 attached=false
08-29 20:52:00.296 W/ActivityManager( 1351): setHasOverlayUi called on unknown pid: 8395
08-29 20:52:00.301 E/system_server( 1351): Cannot read thread CPU times for PID 1351
08-29 20:52:00.303 I/BpBinder( 1205): onLastStrongRef automatically unlinking death recipients: <uncached descriptor>
08-29 20:52:00.330 D/RecentTaskMonitor( 1351): VAddressShellNotifier#notifyOpenTab :{"uri":"player\/tab","need_response":false,"params":{"id":"task:2","displayId":0,"userId":0,"name":"Mumu Launcher","originName":"Mumu Launcher","packageName":"com.mumu.launcher","launcher":true,"newTask":false,"icon":"iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAHOSURBVEiJ7ZTBatRAGMd\/mclacMCF7ta20pNPsFBEjQf7CB48CsUX8GDFd\/DiE3hp0ZsHMZrTQtiTgr34AuJhhT2IlsS1ktnk87BJmm2TdqnrrT8YSGa+\/L\/v+89M4IIF4+bjvxEDCXBl0cIuMASyfMSAWaS4aK0lCALp9\/vSbrcFkHyNENzwH6yLtdbi+76EYSgFxhght2sf4k+QfKyxTp1R+RAwvu9jjMHzvHJxNBoVMd8Oz2HXjC2DwUCstXKcJEmk0+kIIB8aOmii1pYmqnbNU\/kQyN69fyuPXizLr8ODUmhn92ptgjiOhYbT5RwTtwBBEGCM4eatGzx5uUIyGQPwfPsHl9w23b3PRDY7avdhjyUyut0uURQBtIAJzG7yz2pm785tnr5aK8UBHu8ul8+9a6tsbqyzubGOcqp1AvC7sKt6dtfyzF89z2srR3Pv+msm6eQUR2tZBcb5mEkwLt611mSS8ubLff7YCBHIMlAKtu6mtJSD64A6UXipU7Y91+3LUtjbgQfPoKUV37d7J2KsTWu\/Pe2iHQXpqbi7NE\/0LLUdWGvRriJLodgC5U47sdbWCjXN1yU8YPojO+9ovAcFlxvm52V8dsgFFf4Csc8O5d4NxXMAAAAASUVORK5CYII=","canChangeAppOrientation":false,"canReceiveFile":false,"pid":5877,"uid":1000,"action":"open"}}
08-29 20:52:00.335 D/RecentTaskMonitor( 1351): VAddressShellNotifier#notifyCloseTab :{"uri":"player\/tab","need_response":false,"params":{"id":"task:3045","action":"close"}}
08-29 20:52:00.335 V/WindowManager( 1351): Unknown focus tokens, dropping reportFocusChanged
08-29 20:52:00.349 D/Launcher.Workspace( 5877): setInsets: insets = Rect(0, 36 - 0, 0)
08-29 20:52:00.364 W/ContextImpl( 5877): Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1181 android.content.ContextWrapper.sendBroadcast:479 com.mumu.launcher.Launcher.onResume:215 android.app.Instrumentation.callActivityOnResume:1492 android.app.Activity.performResume:8198 
08-29 20:52:00.368 D/ThrowableReceiver( 7736): intent.getAction() == Intent { act=android.intent.action.LAUNCHER_RESUME flg=0x10 pkg=com.mumu.store cmp=com.mumu.store/.install.ThrowableReceiver }
08-29 20:52:00.369 D/ThrowableReceiver( 7736): receive android.intent.action.LAUNCHER_RESUME
08-29 20:52:00.371 D/Launcher.Workspace( 5877): setInsets: insets = Rect(0, 36 - 0, 0)
08-29 20:52:00.372 D/gralloc_x86( 5877): gralloc_alloc: Creating ashmem region of size 3690496
08-29 20:52:00.382 D/m.mumu.launche( 5877): Features: 0 0 0
08-29 20:52:00.382 E/m.mumu.launche( 5877): vulkan version is 4206872
08-29 20:52:00.383 D/gralloc_x86( 5877): gralloc_alloc: Creating ashmem region of size 3690496
08-29 20:52:00.384 D/gralloc_x86( 5877): gralloc_alloc: Creating ashmem region of size 3690496
08-29 20:52:00.433 D/SntpClient( 1351): request time failed: java.net.SocketTimeoutException: Poll timed out
08-29 20:52:00.429 I/Binder:1180_2( 1180): type=1400 audit(0.0:42157): avc: denied { use } for path="socket:[657462]" dev="sockfs" ino=657462 scontext=u:r:init:s0 tcontext=u:r:system_server:s0 tclass=fd permissive=1
08-29 20:52:00.429 I/Binder:1180_2( 1180): type=1400 audit(0.0:42158): avc: denied { read write } for path="socket:[657462]" dev="sockfs" ino=657462 scontext=u:r:init:s0 tcontext=u:r:system_server:s0 tclass=udp_socket permissive=1
08-29 20:52:00.429 I/Binder:1180_2( 1180): type=1400 audit(0.0:42159): avc: denied { getopt } for scontext=u:r:init:s0 tcontext=u:r:system_server:s0 tclass=udp_socket permissive=1
08-29 20:52:00.429 I/Binder:1180_2( 1180): type=1400 audit(0.0:42160): avc: denied { setopt } for lport=59999 scontext=u:r:init:s0 tcontext=u:r:system_server:s0 tclass=udp_socket permissive=1
08-29 20:52:00.436 I/ps      ( 8514): type=1400 audit(0.0:42161): avc: denied { read } for name="stat" dev="proc" ino=2967 scontext=u:r:adbd:s0 tcontext=u:r:zygote:s0 tclass=file permissive=1
08-29 20:52:00.660 I/lowmemorykiller( 1103): Kill 'com.android.packageinstaller' (7500), uid 10013, oom_score_adj 935 to free 64768kB rss, 0kB swap; reason: device is not responding
08-29 20:52:00.662 I/ActivityManager( 1351): Process com.android.packageinstaller (pid 7500) has died: cch+35 CEM 
08-29 20:52:00.662 I/libprocessgroup( 1351): Successfully killed process cgroup uid 10013 pid 7500 in 0ms
08-29 20:52:00.662 I/Zygote  ( 1181): Process 7500 exited due to signal 9 (Killed)
08-29 20:52:00.784 W/InputManager-JNI( 1351): Input channel object '5d3c754 Toast (client)' was disposed without first being removed with the input manager!
08-29 20:52:00.786 W/NotificationService( 1351): Toast already killed. pkg=com.bilibili.azurlane token=android.os.BinderProxy@962ee3c
08-29 20:52:00.789 W/ToastPresenter( 1539): Error calling back com.bilibili.azurlane to notify onToastHide()
08-29 20:52:00.789 W/ToastPresenter( 1539): android.os.DeadObjectException
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.os.BinderProxy.transactNative(Native Method)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.os.BinderProxy.transact(BinderProxy.java:571)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.app.ITransientNotificationCallback$Stub$Proxy.onToastHidden(ITransientNotificationCallback.java:145)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.widget.ToastPresenter.hide(ToastPresenter.java:262)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at com.android.systemui.toast.ToastUI$ToastOutAnimatorListener.onAnimationEnd(ToastUI.java:216)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.animation.Animator$AnimatorListener.onAnimationEnd(Animator.java:555)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.animation.AnimatorSet.endAnimation(AnimatorSet.java:1301)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.animation.AnimatorSet.doAnimationFrame(AnimatorSet.java:1086)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.animation.AnimationHandler.doAnimationFrame(AnimationHandler.java:146)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.animation.AnimationHandler.access$100(AnimationHandler.java:37)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.animation.AnimationHandler$1.doFrame(AnimationHandler.java:54)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1045)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.view.Choreographer.doCallbacks(Choreographer.java:845)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.view.Choreographer.doFrame(Choreographer.java:775)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1032)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.os.Handler.handleCallback(Handler.java:938)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.os.Handler.dispatchMessage(Handler.java:99)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at android.app.ActivityThread.main(ActivityThread.java:8060)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
08-29 20:52:00.789 W/ToastPresenter( 1539): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
08-29 20:52:00.874 D/drawPreviewItem( 5877): drawPreviewItem: drawPreviewItem
08-29 20:52:00.874 D/drawPreviewItem( 5877): drawPreviewItem: drawPreviewItem
08-29 20:52:00.874 D/drawPreviewItem( 5877): drawPreviewItem: drawPreviewItem
08-29 20:52:00.874 D/drawPreviewItem( 5877): drawPreviewItem: drawPreviewItem
08-29 20:52:00.874 D/drawPreviewItem( 5877): drawPreviewItem: drawPreviewItem
08-29 20:52:00.874 D/drawPreviewItem( 5877): drawPreviewItem: drawPreviewItem
08-29 20:52:00.874 D/drawPreviewItem( 5877): drawPreviewItem: drawPreviewItem
08-29 20:52:00.882 W/OpenGLRenderer( 5877): Incorrectly called buildLayer on View: E1, destroying layer...
08-29 20:52:00.882 W/OpenGLRenderer( 5877): Incorrectly called buildLayer on View: E1, destroying layer...
08-29 20:52:00.887 W/SocketClient( 1180): write error (Broken pipe)
08-29 20:52:00.887 W/SocketClient( 1180): write error (Broken pipe)
08-29 20:52:00.887 W/resolv  ( 1180): GetAddrInfoHandler::run: Error writing DNS result to client uid 10055 pid 8395: Broken pipe
08-29 20:52:00.887 W/resolv  ( 1180): GetAddrInfoHandler::run: Error writing DNS result to client uid 10055 pid 8395: Broken pipe
08-29 20:52:00.892 W/SocketClient( 1180): write error (Broken pipe)
08-29 20:52:00.892 W/resolv  ( 1180): GetAddrInfoHandler::run: Error writing DNS result to client uid 10055 pid 8395: Broken pipe
08-29 20:52:05.439 D/SntpClient( 1351): request time failed: java.net.SocketTimeoutException: Poll timed out
08-29 20:52:05.439 D/SntpClient( 1351): request time failed
08-29 20:52:05.440 D/NetworkTimeUpdateService( 1351): Stale NTP fix; forcing refresh
08-29 20:52:05.436 I/Dns_100_1000( 1180): type=1400 audit(0.0:42168): avc: denied { getopt } for scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=udp_socket permissive=1
08-29 20:52:05.436 I/Dns_100_1000( 1180): type=1400 audit(0.0:42169): avc: denied { bind } for scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=udp_socket permissive=1
08-29 20:52:05.436 I/Dns_100_1000( 1180): type=1400 audit(0.0:42170): avc: denied { node_bind } for src=53265 scontext=u:r:init:s0 tcontext=u:object_r:node:s0 tclass=udp_socket permissive=1
08-29 20:52:05.436 I/Dns_100_1000( 1180): type=1400 audit(0.0:42171): avc: denied { write } for laddr=********* lport=53265 faddr=********* fport=53 scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=udp_socket permissive=1
08-29 20:52:05.443 I/Dns_100_1000( 1180): type=1400 audit(0.0:42172): avc: denied { read } for laddr=********* lport=53265 faddr=********* fport=53 scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=udp_socket permissive=1
08-29 20:52:10.451 D/SntpClient( 1351): request time failed: java.net.SocketTimeoutException: Poll timed out
08-29 20:52:11.641 W/WindowManager( 1351): removeWindowToken: Attempted to remove non-existing token: android.os.Binder@576cfc1
08-29 20:52:15.458 D/SntpClient( 1351): request time failed: java.net.SocketTimeoutException: Poll timed out
08-29 20:52:20.463 D/SntpClient( 1351): request time failed: java.net.SocketTimeoutException: Poll timed out
08-29 20:52:23.359 I/netd    ( 1180): type=1400 audit(0.0:42173): avc: denied { create } for scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_tcpdiag_socket permissive=1
08-29 20:52:23.359 I/netd    ( 1180): type=1400 audit(0.0:42174): avc: denied { connect } for scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_tcpdiag_socket permissive=1
08-29 20:52:23.359 I/netd    ( 1180): type=1400 audit(0.0:42175): avc: denied { write } for path="socket:[657493]" dev="sockfs" ino=657493 scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_tcpdiag_socket permissive=1
08-29 20:52:23.359 I/netd    ( 1180): type=1400 audit(0.0:42176): avc: denied { nlmsg_read } for scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_tcpdiag_socket permissive=1
08-29 20:52:23.529 I/wifi@1.0-servic( 1202): type=1400 audit(0.0:42177): avc: denied { write } for scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_route_socket permissive=1
08-29 20:52:25.468 D/SntpClient( 1351): request time failed: java.net.SocketTimeoutException: Poll timed out
08-29 20:52:25.468 D/SntpClient( 1351): request time failed
08-29 20:52:25.779 I/Thread-37( 2001): type=1400 audit(0.0:42180): avc: denied { use } for path="/dev/null" dev="tmpfs" ino=14358 scontext=u:r:mediaprovider_app:s0:c32,c256,c512,c768 tcontext=u:r:kernel:s0 tclass=fd permissive=1 app=com.android.providers.media.module
08-29 20:52:25.810 D/ProcessState( 8553): Binder ioctl to enable oneway spam detection failed: Invalid argument
08-29 20:52:25.811 W/ActivityManager( 1351): Background start not allowed: service Intent { cmp=com.github.uiautomator/.Service } to com.github.uiautomator/.Service from pid=8553 uid=2000 pkg=com.android.shell startFg?=false
08-29 20:52:25.806 I/servicemanager( 1104): type=1400 audit(0.0:42181): avc: denied { transfer } for scontext=u:r:init:s0 tcontext=u:r:adbd:s0 tclass=binder permissive=1
08-29 20:52:25.806 I/cmd     ( 8553): type=1400 audit(0.0:42182): avc: denied { write } for path="pipe:[652255]" dev="pipefs" ino=652255 scontext=u:r:system_server:s0 tcontext=u:r:adbd:s0 tclass=fifo_file permissive=1
08-29 20:52:25.806 I/Binder:1351_A( 1351): type=1400 audit(0.0:42183): avc: denied { call } for scontext=u:r:system_server:s0 tcontext=u:r:adbd:s0 tclass=binder permissive=1
08-29 20:52:41.766 I/netd    ( 1180): type=1400 audit(0.0:42184): avc: denied { read } for scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_kobject_uevent_socket permissive=1
08-29 20:52:41.829 I/surfaceflinger( 1205): type=1400 audit(0.0:42185): avc: denied { call } for scontext=u:r:init:s0 tcontext=u:r:system_app:s0 tclass=binder permissive=1
08-29 20:52:41.829 I/surfaceflinger( 1205): type=1400 audit(0.0:42186): avc: denied { transfer } for scontext=u:r:init:s0 tcontext=u:r:system_app:s0 tclass=binder permissive=1
08-29 20:52:46.759 I/health@2.0-serv( 1197): type=1400 audit(0.0:42187): avc: denied { read } for name="present" dev="sysfs" ino=9653 scontext=u:r:init:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
08-29 20:52:46.759 I/health@2.0-serv( 1197): type=1400 audit(0.0:42188): avc: denied { open } for path="/sys/devices/virtual/power_supply/battery/present" dev="sysfs" ino=9653 scontext=u:r:init:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
