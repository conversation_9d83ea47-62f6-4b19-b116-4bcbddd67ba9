--------- beginning of main
08-29 20:13:04.057 W/System.err( 4962): java.lang.reflect.InvocationTargetException
08-29 20:13:04.057 W/System.err( 4962): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.device.DeviceHelper.checkHasPermission(DeviceHelper.java:14)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdReader.readCommon(IdReader.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdReader.read(IdReader.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdExternalStorage.load(IdExternalStorage.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdExternalStorageCache.loadSync(IdExternalStorageCache.java:4)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdExternalStorageCache.load(IdExternalStorageCache.java:4)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdExternalStorageCache.init(IdExternalStorageCache.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdEnvironmentManager.init(IdEnvironmentManager.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.Udids.initialize(Udids.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.device.DeviceUtil.initUdid(DeviceUtil.java:4)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.device.DeviceUtil.setAgreePrivacyPolicy(DeviceUtil.java:3)
08-29 20:13:04.058 W/System.err( 4962): 	at com.gsc.route_service.service.InitAgreementRouteService.ignoreAgreement(InitAgreementRouteService.java:5)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.gsc_agreement_library.AgreementWebActivity.run(AgreementWebActivity.java:5)
08-29 20:13:04.058 W/System.err( 4962): 	at android.os.Handler.handleCallback(Handler.java:938)
08-29 20:13:04.058 W/System.err( 4962): 	at android.os.Handler.dispatchMessage(Handler.java:99)
08-29 20:13:04.058 W/System.err( 4962): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:13:04.058 W/System.err( 4962): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:13:04.058 W/System.err( 4962): 	at android.app.ActivityThread.main(ActivityThread.java:8060)
08-29 20:13:04.058 W/System.err( 4962): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:13:04.058 W/System.err( 4962): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
08-29 20:13:04.058 W/System.err( 4962): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
08-29 20:13:04.062 W/System.err( 4962): Caused by: java.lang.NullPointerException: Attempt to invoke virtual method 'int android.content.Context.checkPermission(java.lang.String, int, int)' on a null object reference
08-29 20:13:04.063 W/System.err( 4962): 	at androidx.core.content.ContextCompat.checkSelfPermission(ContextCompat.java:555)
08-29 20:13:04.063 W/System.err( 4962): 	... 22 more
08-29 20:13:04.067 W/System.err( 4962): java.lang.Throwable: you must call IdEnvironmentManager.init(context) first
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.commonlib.udid.IdEnvironmentManager.initUdid(IdEnvironmentManager.java:13)
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.commonlib.udid.Udids.initialize(Udids.java:2)
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.commonlib.device.DeviceUtil.initUdid(DeviceUtil.java:4)
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.commonlib.device.DeviceUtil.setAgreePrivacyPolicy(DeviceUtil.java:3)
08-29 20:13:04.067 W/System.err( 4962): 	at com.gsc.route_service.service.InitAgreementRouteService.ignoreAgreement(InitAgreementRouteService.java:5)
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.gsc_agreement_library.AgreementWebActivity.run(AgreementWebActivity.java:5)
08-29 20:13:04.067 W/System.err( 4962): 	at android.os.Handler.handleCallback(Handler.java:938)
08-29 20:13:04.067 W/System.err( 4962): 	at android.os.Handler.dispatchMessage(Handler.java:99)
08-29 20:13:04.067 W/System.err( 4962): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:13:04.067 W/System.err( 4962): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:13:04.067 W/System.err( 4962): 	at android.app.ActivityThread.main(ActivityThread.java:8060)
08-29 20:13:04.068 W/System.err( 4962): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:13:04.068 W/System.err( 4962): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
08-29 20:13:04.068 W/System.err( 4962): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
08-29 20:36:56.197 W/System.err( 5877): com.mumu.launcher.api.service.ApiError: 应用不存在
08-29 20:36:56.197 W/System.err( 5877): 	at com.mumu.launcher.api.service.d$b$b.a(CoroutineCallAdapterFactory.kt:50)
08-29 20:36:56.197 W/System.err( 5877): 	at G2.p$a.b(OkHttpCall.java:11)
08-29 20:36:56.197 W/System.err( 5877): 	at w2.e$a.run(RealCall.kt:52)
08-29 20:36:56.197 W/System.err( 5877): 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
08-29 20:36:56.197 W/System.err( 5877): 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
08-29 20:36:56.197 W/System.err( 5877): 	at java.lang.Thread.run(Thread.java:920)
08-29 20:36:56.198 W/System.err( 5877): android.content.pm.PackageManager$NameNotFoundException: com.YoStarEN.AzurLane
08-29 20:36:56.198 W/System.err( 5877): 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:241)
08-29 20:36:56.198 W/System.err( 5877): 	at android.app.ApplicationPackageManager.getPackageInfo(ApplicationPackageManager.java:213)
08-29 20:36:56.198 W/System.err( 5877): 	at U.g.b(PackageUtil.kt:16)
08-29 20:36:56.198 W/System.err( 5877): 	at U.g.d(PackageUtil.kt:11)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.F1.b(ShortcutInfo.java:29)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu.b(PopupMenu.kt:13)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu.o(PopupMenu.kt:6)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu.i(PopupMenu.kt:1)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu$f.a(PopupMenu.kt:87)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu$f.invoke(PopupMenu.kt:3)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.api.service.b$a.invokeSuspend(ApiRepository.kt:94)
08-29 20:36:56.198 W/System.err( 5877): 	at kotlin.coroutines.jvm.internal.a.resumeWith(ContinuationImpl.kt:12)
08-29 20:36:56.198 W/System.err( 5877): 	at l2.X.run(DispatchedTask.kt:110)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Handler.handleCallback(Handler.java:938)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Handler.dispatchMessage(Handler.java:99)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:36:56.198 W/System.err( 5877): 	at android.app.ActivityThread.main(ActivityThread.java:8060)
08-29 20:36:56.198 W/System.err( 5877): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:36:56.198 W/System.err( 5877): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
08-29 20:36:56.198 W/System.err( 5877): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
08-29 20:36:56.198 W/System.err( 5877): android.content.pm.PackageManager$NameNotFoundException: com.YoStarEN.AzurLane
08-29 20:36:56.198 W/System.err( 5877): 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:241)
08-29 20:36:56.198 W/System.err( 5877): 	at android.app.ApplicationPackageManager.getPackageInfo(ApplicationPackageManager.java:213)
08-29 20:36:56.198 W/System.err( 5877): 	at U.g.b(PackageUtil.kt:16)
08-29 20:36:56.198 W/System.err( 5877): 	at U.g.d(PackageUtil.kt:11)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.F1.b(ShortcutInfo.java:29)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.F1.a(ShortcutInfo.java:1)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu.a(PopupMenu.kt:13)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu.o(PopupMenu.kt:41)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu.i(PopupMenu.kt:1)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu$f.a(PopupMenu.kt:87)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu$f.invoke(PopupMenu.kt:3)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.api.service.b$a.invokeSuspend(ApiRepository.kt:94)
08-29 20:36:56.198 W/System.err( 5877): 	at kotlin.coroutines.jvm.internal.a.resumeWith(ContinuationImpl.kt:12)
08-29 20:36:56.198 W/System.err( 5877): 	at l2.X.run(DispatchedTask.kt:110)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Handler.handleCallback(Handler.java:938)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Handler.dispatchMessage(Handler.java:99)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:36:56.198 W/System.err( 5877): 	at android.app.ActivityThread.main(ActivityThread.java:8060)
08-29 20:36:56.198 W/System.err( 5877): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:36:56.198 W/System.err( 5877): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
08-29 20:36:56.198 W/System.err( 5877): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
08-29 20:36:56.198 W/System.err( 5877): android.content.pm.PackageManager$NameNotFoundException: com.YoStarEN.AzurLane
08-29 20:36:56.198 W/System.err( 5877): 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:241)
08-29 20:36:56.198 W/System.err( 5877): 	at android.app.ApplicationPackageManager.getPackageInfo(ApplicationPackageManager.java:213)
08-29 20:36:56.198 W/System.err( 5877): 	at U.g.b(PackageUtil.kt:16)
08-29 20:36:56.198 W/System.err( 5877): 	at U.g.d(PackageUtil.kt:11)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.F1.e(ShortcutInfo.java:41)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu.e(PopupMenu.kt:13)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu.o(PopupMenu.kt:123)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu.i(PopupMenu.kt:1)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu$f.a(PopupMenu.kt:87)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.popup.PopupMenu$f.invoke(PopupMenu.kt:3)
08-29 20:36:56.198 W/System.err( 5877): 	at com.mumu.launcher.api.service.b$a.invokeSuspend(ApiRepository.kt:94)
08-29 20:36:56.198 W/System.err( 5877): 	at kotlin.coroutines.jvm.internal.a.resumeWith(ContinuationImpl.kt:12)
08-29 20:36:56.198 W/System.err( 5877): 	at l2.X.run(DispatchedTask.kt:110)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Handler.handleCallback(Handler.java:938)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Handler.dispatchMessage(Handler.java:99)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:36:56.198 W/System.err( 5877): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:36:56.198 W/System.err( 5877): 	at android.app.ActivityThread.main(ActivityThread.java:8060)
08-29 20:36:56.198 W/System.err( 5877): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:36:56.198 W/System.err( 5877): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
08-29 20:36:56.198 W/System.err( 5877): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
08-29 20:37:03.026 W/System.err( 5957): com.mumu.vending.installing.ApkInstaller$InstallError: Failure [INSTALL_PARSE_FAILED_NO_CERTIFICATES: Scanning Failed.: No signature found in package of version 2 or newer for package com.YoStarEN.AzurLane]
08-29 20:37:03.026 W/System.err( 5957): 
08-29 20:37:03.026 W/System.err( 5957):  true 
08-29 20:37:03.026 W/System.err( 5957): 	at com.mumu.vending.installing.ApkInstaller.e(Proguard:488)
08-29 20:37:03.026 W/System.err( 5957): 	at com.mumu.vending.installing.ApkInstaller.f(Proguard:65)
08-29 20:37:03.026 W/System.err( 5957): 	at com.mumu.store.install.InstallService.h(Proguard:76)
08-29 20:37:03.026 W/System.err( 5957): 	at com.mumu.store.install.InstallService.i(Proguard:22)
08-29 20:37:03.026 W/System.err( 5957): 	at com.mumu.store.install.InstallService.g(Proguard:6)
08-29 20:37:03.026 W/System.err( 5957): 	at com.mumu.store.install.InstallService.c(Proguard:1)
08-29 20:37:03.026 W/System.err( 5957): 	at com.mumu.store.install.InstallService$b.handleMessage(Proguard:89)
08-29 20:37:03.026 W/System.err( 5957): 	at android.os.Handler.dispatchMessage(Handler.java:106)
08-29 20:37:03.026 W/System.err( 5957): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:37:03.026 W/System.err( 5957): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:37:03.026 W/System.err( 5957): 	at android.os.HandlerThread.run(HandlerThread.java:67)
08-29 20:37:08.354 W/System.err( 5957): com.mumu.store.data.remote.ApiError: 应用不存在
08-29 20:37:08.354 W/System.err( 5957): 	at com.mumu.store.data.remote.e.c(Proguard:50)
08-29 20:37:08.354 W/System.err( 5957): 	at com.mumu.store.data.remote.e.a(Proguard:3)
08-29 20:37:08.354 W/System.err( 5957): 	at retrofit2.x.f(Proguard:59)
08-29 20:37:08.354 W/System.err( 5957): 	at retrofit2.x.execute(Proguard:25)
08-29 20:37:08.354 W/System.err( 5957): 	at b3.c.E(Proguard:24)
08-29 20:37:08.354 W/System.err( 5957): 	at A1.h.a(Proguard:15)
08-29 20:37:08.354 W/System.err( 5957): 	at b3.a.E(Proguard:8)
08-29 20:37:08.354 W/System.err( 5957): 	at A1.h.a(Proguard:15)
08-29 20:37:08.354 W/System.err( 5957): 	at io.reactivex.internal.operators.observable.x$b.run(Proguard:7)
08-29 20:37:08.354 W/System.err( 5957): 	at A1.n$a.run(Proguard:10)
08-29 20:37:08.354 W/System.err( 5957): 	at io.reactivex.internal.schedulers.h.run(Proguard:14)
08-29 20:37:08.354 W/System.err( 5957): 	at io.reactivex.internal.schedulers.h.call(Proguard:1)
08-29 20:37:08.354 W/System.err( 5957): 	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
08-29 20:37:08.354 W/System.err( 5957): 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
08-29 20:37:08.354 W/System.err( 5957): 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
08-29 20:37:08.354 W/System.err( 5957): 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
08-29 20:37:08.354 W/System.err( 5957): 	at java.lang.Thread.run(Thread.java:920)
08-29 20:37:14.505 W/System.err( 5957): com.mumu.vending.installing.ApkInstaller$InstallError: Failure [INSTALL_PARSE_FAILED_NO_CERTIFICATES: Scanning Failed.: No signature found in package of version 2 or newer for package com.YoStarEN.AzurLane]
08-29 20:37:14.505 W/System.err( 5957): 
08-29 20:37:14.505 W/System.err( 5957):  true 
08-29 20:37:14.506 W/System.err( 5957): 	at com.mumu.vending.installing.ApkInstaller.e(Proguard:488)
08-29 20:37:14.506 W/System.err( 5957): 	at com.mumu.vending.installing.ApkInstaller.f(Proguard:65)
08-29 20:37:14.506 W/System.err( 5957): 	at com.mumu.store.install.InstallService.h(Proguard:76)
08-29 20:37:14.506 W/System.err( 5957): 	at com.mumu.store.install.InstallService.i(Proguard:22)
08-29 20:37:14.506 W/System.err( 5957): 	at com.mumu.store.install.InstallService.g(Proguard:6)
08-29 20:37:14.506 W/System.err( 5957): 	at com.mumu.store.install.InstallService.c(Proguard:1)
08-29 20:37:14.506 W/System.err( 5957): 	at com.mumu.store.install.InstallService$b.handleMessage(Proguard:89)
08-29 20:37:14.506 W/System.err( 5957): 	at android.os.Handler.dispatchMessage(Handler.java:106)
08-29 20:37:14.506 W/System.err( 5957): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:37:14.506 W/System.err( 5957): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:37:14.506 W/System.err( 5957): 	at android.os.HandlerThread.run(HandlerThread.java:67)
08-29 20:40:49.745 W/System.err( 5957): com.mumu.vending.installing.ApkInstaller$InstallError: Failure [INSTALL_PARSE_FAILED_NO_CERTIFICATES: Failed collecting certificates for /data/app/vmdl738675571.tmp/base.apk: Failed to collect certificates from /data/app/vmdl738675571.tmp/base.apk: Attempt to get length of null array]
08-29 20:40:49.745 W/System.err( 5957): 
08-29 20:40:49.745 W/System.err( 5957):  true 
08-29 20:40:49.745 W/System.err( 5957): 	at com.mumu.vending.installing.ApkInstaller.e(Proguard:488)
08-29 20:40:49.745 W/System.err( 5957): 	at com.mumu.vending.installing.ApkInstaller.f(Proguard:65)
08-29 20:40:49.745 W/System.err( 5957): 	at com.mumu.store.install.InstallService.h(Proguard:76)
08-29 20:40:49.745 W/System.err( 5957): 	at com.mumu.store.install.InstallService.i(Proguard:22)
08-29 20:40:49.745 W/System.err( 5957): 	at com.mumu.store.install.InstallService.g(Proguard:6)
08-29 20:40:49.745 W/System.err( 5957): 	at com.mumu.store.install.InstallService.c(Proguard:1)
08-29 20:40:49.745 W/System.err( 5957): 	at com.mumu.store.install.InstallService$b.handleMessage(Proguard:89)
08-29 20:40:49.745 W/System.err( 5957): 	at android.os.Handler.dispatchMessage(Handler.java:106)
08-29 20:40:49.745 W/System.err( 5957): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:40:49.745 W/System.err( 5957): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:40:49.745 W/System.err( 5957): 	at android.os.HandlerThread.run(HandlerThread.java:67)
08-29 20:41:31.935 W/System.err( 5957): com.mumu.vending.installing.ApkInstaller$InstallError: Failure [INSTALL_PARSE_FAILED_NO_CERTIFICATES: Failed collecting certificates for /data/app/vmdl1305769204.tmp/base.apk: Failed to collect certificates from /data/app/vmdl1305769204.tmp/base.apk: Attempt to get length of null array]
08-29 20:41:31.935 W/System.err( 5957): 
08-29 20:41:31.935 W/System.err( 5957):  true 
08-29 20:41:31.935 W/System.err( 5957): 	at com.mumu.vending.installing.ApkInstaller.e(Proguard:488)
08-29 20:41:31.935 W/System.err( 5957): 	at com.mumu.vending.installing.ApkInstaller.f(Proguard:65)
08-29 20:41:31.935 W/System.err( 5957): 	at com.mumu.store.install.InstallService.h(Proguard:76)
08-29 20:41:31.935 W/System.err( 5957): 	at com.mumu.store.install.InstallService.i(Proguard:22)
08-29 20:41:31.935 W/System.err( 5957): 	at com.mumu.store.install.InstallService.g(Proguard:6)
08-29 20:41:31.935 W/System.err( 5957): 	at com.mumu.store.install.InstallService.c(Proguard:1)
08-29 20:41:31.935 W/System.err( 5957): 	at com.mumu.store.install.InstallService$b.handleMessage(Proguard:89)
08-29 20:41:31.935 W/System.err( 5957): 	at android.os.Handler.dispatchMessage(Handler.java:106)
08-29 20:41:31.935 W/System.err( 5957): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:41:31.935 W/System.err( 5957): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:41:31.935 W/System.err( 5957): 	at android.os.HandlerThread.run(HandlerThread.java:67)
