@echo off
echo ========================================
echo APK安装测试脚本
echo ========================================

set BILIBILI_APK=apk_build\com.bilibili.AzurLane-.patched.apk
set YOSTAR_APK=apk_build\com.YoStarEN.AzurLane-9.1.1.patched.apk

echo 选择要安装的APK版本:
echo 1. B站版本 (com.bilibili.AzurLane)
echo 2. 英文版本 (com.YoStarEN.AzurLane)
set /p choice=请输入选择 (1 或 2): 

if "%choice%"=="1" (
    set APK_FILE=%BILIBILI_APK%
    set PACKAGE_NAME=com.bilibili.AzurLane
    set ACTIVITY_NAME=com.bilibili.AzurLane/.MainActivity
) else if "%choice%"=="2" (
    set APK_FILE=%YOSTAR_APK%
    set PACKAGE_NAME=com.YoStarEN.AzurLane
    set ACTIVITY_NAME=com.YoStarEN.AzurLane/.MainActivity
) else (
    echo 无效选择，退出
    pause
    exit /b
)

echo.
echo 检查APK文件是否存在...
if not exist "%APK_FILE%" (
    echo 错误: APK文件不存在: %APK_FILE%
    echo 请先运行 python pieperseus.py 构建APK
    pause
    exit /b
)

echo.
echo 卸载旧版本...
adb uninstall %PACKAGE_NAME%

echo.
echo 安装新APK: %APK_FILE%
adb install "%APK_FILE%"

if %errorlevel% neq 0 (
    echo 安装失败！
    pause
    exit /b
)

echo.
echo 安装成功！启动应用...
adb shell am start -n %ACTIVITY_NAME%

echo.
echo 检查应用进程...
adb shell ps | findstr %PACKAGE_NAME%

echo.
echo 如果应用闪退，请运行 debug_mumu.bat 查看日志
pause
