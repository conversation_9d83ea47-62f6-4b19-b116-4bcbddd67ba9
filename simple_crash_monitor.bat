@echo off
chcp 65001 >nul
title Simple Crash Monitor
color 0C

echo ========================================
echo Simple Crash Monitor for Bilibili
echo ========================================

set PATH=%PATH%;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools

echo [STEP 1] Clearing logs...
adb logcat -c

echo [STEP 2] Starting log monitor in background...
start "Crash Monitor" cmd /c "adb logcat -v time > crash_monitor.log"

timeout /t 2 /nobreak >nul

echo [STEP 3] Force stopping app...
adb shell am force-stop com.bilibili.azurlane

echo [STEP 4] Launching app...
echo Launch time: %time%
adb shell monkey -p com.bilibili.azurlane -c android.intent.category.LAUNCHER 1

echo [STEP 5] Monitoring for 15 seconds...
echo Watching for crashes...

for /L %%i in (1,1,15) do (
    echo Second %%i/15...
    
    adb shell ps | findstr com.bilibili.azurlane >nul
    if %errorlevel% neq 0 (
        echo CRASH DETECTED at second %%i!
        goto :crash_found
    )
    
    timeout /t 1 /nobreak >nul
)

echo App survived 15 seconds!
goto :end

:crash_found
echo.
echo ========================================
echo CRASH DETECTED! Collecting logs...
echo ========================================

REM Stop the log monitor
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Crash Monitor" >nul 2>&1

timeout /t 3 /nobreak >nul

echo.
echo Recent crash logs:
adb logcat -d -t 100 | findstr /i "fatal error exception androidruntime"

echo.
echo App-specific logs:
adb logcat -d | findstr /i "bilibili azurlane"

echo.
echo Full crash log saved to: crash_monitor.log
goto :end

:end
REM Stop log monitor if still running
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Crash Monitor" >nul 2>&1

echo.
echo Monitoring completed at %time%
pause
