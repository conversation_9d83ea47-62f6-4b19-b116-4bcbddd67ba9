@echo off
chcp 65001 >nul
title WebView Crash Fix
color 0A

echo ========================================
echo WebView Crash Fix for Bilibili Version
echo ========================================

echo [INFO] This script will fix the AgreementWebActivity crash
echo [INFO] by patching the WebView URL loading issue

echo [STEP 1] Checking if APK is decompiled...
if not exist "apk_build\com.bilibili.azurlane" (
    echo [ERROR] APK not decompiled yet!
    echo Please run: python pieperseus.py --no-skip
    pause
    exit /b 1
)

echo [STEP 2] Looking for Agreement activity files...
cd apk_build\com.bilibili.azurlane

echo Searching for AgreementWebActivity...
dir /s /b *Agreement*.smali > agreement_files.txt

if not exist agreement_files.txt (
    echo [ERROR] No agreement files found!
    pause
    exit /b 1
)

echo Found agreement files:
type agreement_files.txt

echo [STEP 3] Creating backup...
if not exist "backup" mkdir backup
for /f %%i in (agreement_files.txt) do (
    echo Backing up %%i
    copy "%%i" "backup\" >nul 2>&1
)

echo [STEP 4] Applying WebView crash fix...

REM Create a simple patch script
echo @echo off > patch_agreement.bat
echo echo Patching WebView crash... >> patch_agreement.bat

REM For each agreement file, add a simple return statement at the beginning of loadUrl method
for /f %%i in (agreement_files.txt) do (
    echo echo Patching %%i >> patch_agreement.bat
    echo powershell -Command "$content = Get-Content '%%i' -Raw; $content = $content -replace '(\.method.*loadUrl.*\r?\n)', '$1    return-void`r`n    # WebView crash fix - skip URL loading`r`n'; Set-Content '%%i' $content" >> patch_agreement.bat
)

echo Running patch script...
call patch_agreement.bat

echo [STEP 5] Verification...
echo Checking if patches were applied...
findstr /s "WebView crash fix" *.smali
if %errorlevel% == 0 (
    echo [SUCCESS] Patches applied successfully!
) else (
    echo [WARNING] Patches may not have been applied correctly
)

echo [STEP 6] Cleanup...
del agreement_files.txt >nul 2>&1
del patch_agreement.bat >nul 2>&1

cd ..\..

echo [STEP 7] Rebuilding APK...
echo Now rebuilding the APK with WebView fix...
python pieperseus.py

echo.
echo ========================================
echo WebView crash fix completed!
echo ========================================
echo.
echo The APK has been rebuilt with the WebView crash fix.
echo Please test the app again to see if the crash is resolved.

pause
