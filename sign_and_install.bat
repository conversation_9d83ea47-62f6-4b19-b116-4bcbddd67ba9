@echo off
chcp 65001 >nul
title Sign and Install WebView Fixed APK
color 0A

echo ========================================
echo 签名并安装WebView修复版APK
echo ========================================

cd apk_build

echo [步骤 1] 检查APK文件...
if not exist "com.bilibili.azurlane-webview-fixed.apk" (
    echo ❌ 找不到WebView修复版APK文件
    pause
    exit /b 1
)

echo ✅ 找到WebView修复版APK文件

echo [步骤 2] 创建自签名证书...
if not exist "debug.keystore" (
    echo 创建debug.keystore...
    keytool -genkey -v -keystore debug.keystore -alias androiddebugkey -keyalg RSA -keysize 2048 -validity 10000 -storepass android -keypass android -dname "CN=Android Debug,O=Android,C=US"
)

echo [步骤 3] 签名APK...
echo 正在签名APK文件...

REM 使用Java自带的jarsigner签名
"%JAVA_HOME%\bin\jarsigner.exe" -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore debug.keystore -storepass android -keypass android com.bilibili.azurlane-webview-fixed.apk androiddebugkey

if %errorlevel% neq 0 (
    echo ❌ APK签名失败
    echo 尝试使用系统jarsigner...
    jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore debug.keystore -storepass android -keypass android com.bilibili.azurlane-webview-fixed.apk androiddebugkey
    
    if %errorlevel% neq 0 (
        echo ❌ 签名仍然失败，请检查Java环境
        pause
        exit /b 1
    )
)

echo ✅ APK签名成功

echo [步骤 4] 卸载旧版本...
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe uninstall com.bilibili.azurlane

echo [步骤 5] 安装签名后的APK...
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe install com.bilibili.azurlane-webview-fixed.apk

if %errorlevel% eq 0 (
    echo ✅ APK安装成功！
    echo.
    echo ========================================
    echo 🎉 WebView修复版APK安装完成！
    echo ========================================
    echo.
    echo 现在可以在MuMu模拟器中启动应用了
    echo 应用应该不会再在3秒后闪退
) else (
    echo ❌ APK安装失败
    echo.
    echo 手动安装方法：
    echo 1. 将 com.bilibili.azurlane-webview-fixed.apk 复制到桌面
    echo 2. 拖拽到MuMu模拟器中
    echo 3. 点击安装
)

cd ..
pause
