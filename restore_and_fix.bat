@echo off
chcp 65001 >nul
title Restore and Fix WebView
color 0E

echo ========================================
echo Restoring Backup and Applying Precise Fix
echo ========================================

cd apk_build\com.bilibili.AzurLane

echo [STEP 1] Restoring from backup...
if exist "backup\AgreementWebActivity.smali" (
    copy "backup\AgreementWebActivity.smali" "smali\com\base\gsc_agreement_library\AgreementWebActivity.smali"
    echo Restored AgreementWebActivity.smali
) else (
    echo [ERROR] Backup not found!
    pause
    exit /b 1
)

echo [STEP 2] Applying precise WebView fix...
echo Creating precise fix for WebView crash...

REM Create a more targeted fix using PowerShell
powershell -Command "
$file = 'smali\com\base\gsc_agreement_library\AgreementWebActivity.smali'
$content = Get-Content $file -Raw
# Find the loadUrl method and add a simple return at the beginning
$content = $content -replace '(\.method.*loadUrl.*\r?\n)', '$1    # WebView crash prevention fix`r`n    return-void`r`n'
Set-Content $file $content
"

echo [STEP 3] Verifying fix...
findstr /n "WebView crash prevention" "smali\com\base\gsc_agreement_library\AgreementWebActivity.smali"

if %errorlevel% eq 0 (
    echo [SUCCESS] Precise fix applied successfully!
) else (
    echo [WARNING] Fix may not have been applied correctly
)

cd ..\..

echo [STEP 4] Rebuilding APK with precise fix...
apktool b apk_build\com.bilibili.AzurLane -o com.bilibili.azurlane-webview-fixed.apk

if %errorlevel% eq 0 (
    echo [SUCCESS] APK rebuilt successfully!
    
    echo [STEP 5] Installing fixed APK...
    adb uninstall com.bilibili.azurlane
    adb install com.bilibili.azurlane-webview-fixed.apk
    
    if %errorlevel% eq 0 (
        echo [SUCCESS] WebView-fixed APK installed!
        echo.
        echo ========================================
        echo WebView Fix Applied Successfully!
        echo ========================================
        echo.
        echo The app should now start without crashing.
        echo Please test the app now!
    ) else (
        echo [ERROR] APK installation failed
    )
) else (
    echo [ERROR] APK rebuild failed
)

pause
