--------- beginning of main
08-29 20:13:04.057 W/System.err( 4962): java.lang.reflect.InvocationTargetException
08-29 20:13:04.057 W/System.err( 4962): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.device.DeviceHelper.checkHasPermission(DeviceHelper.java:14)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdReader.readCommon(IdReader.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdReader.read(IdReader.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdExternalStorage.load(IdExternalStorage.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdExternalStorageCache.loadSync(IdExternalStorageCache.java:4)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdExternalStorageCache.load(IdExternalStorageCache.java:4)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdExternalStorageCache.init(IdExternalStorageCache.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.IdEnvironmentManager.init(IdEnvironmentManager.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.udid.Udids.initialize(Udids.java:1)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.device.DeviceUtil.initUdid(DeviceUtil.java:4)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.commonlib.device.DeviceUtil.setAgreePrivacyPolicy(DeviceUtil.java:3)
08-29 20:13:04.058 W/System.err( 4962): 	at com.gsc.route_service.service.InitAgreementRouteService.ignoreAgreement(InitAgreementRouteService.java:5)
08-29 20:13:04.058 W/System.err( 4962): 	at com.base.gsc_agreement_library.AgreementWebActivity.run(AgreementWebActivity.java:5)
08-29 20:13:04.058 W/System.err( 4962): 	at android.os.Handler.handleCallback(Handler.java:938)
08-29 20:13:04.058 W/System.err( 4962): 	at android.os.Handler.dispatchMessage(Handler.java:99)
08-29 20:13:04.058 W/System.err( 4962): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:13:04.058 W/System.err( 4962): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:13:04.058 W/System.err( 4962): 	at android.app.ActivityThread.main(ActivityThread.java:8060)
08-29 20:13:04.058 W/System.err( 4962): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:13:04.058 W/System.err( 4962): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
08-29 20:13:04.058 W/System.err( 4962): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
08-29 20:13:04.062 W/System.err( 4962): Caused by: java.lang.NullPointerException: Attempt to invoke virtual method 'int android.content.Context.checkPermission(java.lang.String, int, int)' on a null object reference
08-29 20:13:04.063 W/System.err( 4962): 	at androidx.core.content.ContextCompat.checkSelfPermission(ContextCompat.java:555)
08-29 20:13:04.063 W/System.err( 4962): 	... 22 more
08-29 20:13:04.067 W/System.err( 4962): java.lang.Throwable: you must call IdEnvironmentManager.init(context) first
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.commonlib.udid.IdEnvironmentManager.initUdid(IdEnvironmentManager.java:13)
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.commonlib.udid.Udids.initialize(Udids.java:2)
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.commonlib.device.DeviceUtil.initUdid(DeviceUtil.java:4)
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.commonlib.device.DeviceUtil.setAgreePrivacyPolicy(DeviceUtil.java:3)
08-29 20:13:04.067 W/System.err( 4962): 	at com.gsc.route_service.service.InitAgreementRouteService.ignoreAgreement(InitAgreementRouteService.java:5)
08-29 20:13:04.067 W/System.err( 4962): 	at com.base.gsc_agreement_library.AgreementWebActivity.run(AgreementWebActivity.java:5)
08-29 20:13:04.067 W/System.err( 4962): 	at android.os.Handler.handleCallback(Handler.java:938)
08-29 20:13:04.067 W/System.err( 4962): 	at android.os.Handler.dispatchMessage(Handler.java:99)
08-29 20:13:04.067 W/System.err( 4962): 	at android.os.Looper.loopOnce(Looper.java:201)
08-29 20:13:04.067 W/System.err( 4962): 	at android.os.Looper.loop(Looper.java:288)
08-29 20:13:04.067 W/System.err( 4962): 	at android.app.ActivityThread.main(ActivityThread.java:8060)
08-29 20:13:04.068 W/System.err( 4962): 	at java.lang.reflect.Method.invoke(Native Method)
08-29 20:13:04.068 W/System.err( 4962): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
08-29 20:13:04.068 W/System.err( 4962): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
