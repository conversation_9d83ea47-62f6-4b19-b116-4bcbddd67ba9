@echo off
chcp 65001 >nul
title Simple Startup Crash Test
color 0C

echo ========================================
echo Simple Startup Crash Test
echo Monitoring the critical 3-second window
echo ========================================

set PATH=%PATH%;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools

echo [STEP 1] Preparing environment...
adb logcat -c
adb disconnect 127.0.0.1:16384 >nul 2>&1
adb shell am force-stop com.bilibili.azurlane

echo [STEP 2] Starting log monitoring...
start "Crash Monitor" cmd /c "adb logcat -v time > startup_full_log.txt"
timeout /t 2 /nobreak >nul

echo [STEP 3] Launching app...
echo Launch time: %time%
adb shell monkey -p com.bilibili.azurlane -c android.intent.category.LAUNCHER 1

echo [STEP 4] Monitoring critical 3-second startup window...
echo Second 1...
timeout /t 1 /nobreak >nul
adb shell ps | findstr com.bilibili.azurlane >nul
if %errorlevel% neq 0 (
    echo CRASH at second 1!
    goto :crash_detected
)

echo Second 2...
timeout /t 1 /nobreak >nul
adb shell ps | findstr com.bilibili.azurlane >nul
if %errorlevel% neq 0 (
    echo CRASH at second 2!
    goto :crash_detected
)

echo Second 3...
timeout /t 1 /nobreak >nul
adb shell ps | findstr com.bilibili.azurlane >nul
if %errorlevel% neq 0 (
    echo CRASH at second 3!
    goto :crash_detected
)

echo Second 4...
timeout /t 1 /nobreak >nul
adb shell ps | findstr com.bilibili.azurlane >nul
if %errorlevel% neq 0 (
    echo CRASH at second 4!
    goto :crash_detected
)

echo Second 5...
timeout /t 1 /nobreak >nul
adb shell ps | findstr com.bilibili.azurlane >nul
if %errorlevel% neq 0 (
    echo CRASH at second 5!
    goto :crash_detected
)

echo App survived 5 seconds - checking UI status...
adb shell dumpsys activity activities | findstr "mResumedActivity"
goto :end

:crash_detected
echo.
echo ========================================
echo STARTUP CRASH DETECTED!
echo ========================================
echo Crash time: %time%

REM Stop log monitoring
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Crash Monitor" >nul 2>&1

timeout /t 2 /nobreak >nul

echo.
echo Process status:
adb shell ps | findstr bilibili

echo.
echo Recent crash logs:
adb logcat -d -t 50 | findstr /i "fatal error exception androidruntime"

echo.
echo App-specific logs:
adb logcat -d | findstr /i "bilibili azurlane manjuu"

echo.
echo Full startup log saved to: startup_full_log.txt
goto :end

:end
REM Stop log monitoring if still running
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Crash Monitor" >nul 2>&1

echo.
echo Test completed at %time%
pause
