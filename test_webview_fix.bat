@echo off
chcp 65001 >nul
title Test WebView Fix
color 0A

echo ========================================
echo Testing WebView Fix for Bilibili Version
echo ========================================

set PATH=%PATH%;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools

echo [INFO] Testing the WebView crash fix we applied to pieperseus.py
echo [INFO] The fix should prevent crashes when AgreementWebActivity loads null URLs

echo [STEP 1] Verifying WebView fix was applied...
cd apk_build
if exist "com.bilibili.AzurLane\smali\com\base\gsc_agreement_library\AgreementWebActivity.smali.backup" (
    echo ✅ Backup file found - fix was applied
) else (
    echo ❌ No backup file found
)

echo [STEP 2] Checking fix content...
findstr /n "WebView crash fix" "com.bilibili.AzurLane\smali\com\base\gsc_agreement_library\AgreementWebActivity.smali"
if %errorlevel% eq 0 (
    echo ✅ WebView crash fix code found in smali file
) else (
    echo ❌ WebView crash fix code not found
)

echo [STEP 3] Checking if APK was rebuilt...
if exist "com.bilibili.azurlane-webview-fixed.apk" (
    echo ✅ WebView-fixed APK exists
    dir "com.bilibili.azurlane-webview-fixed.apk"
) else (
    echo ❌ WebView-fixed APK not found
)

echo [STEP 4] Manual installation test...
echo Please manually install the APK in MuMu emulator:
echo 1. Copy com.bilibili.azurlane-webview-fixed.apk to your desktop
echo 2. Drag and drop it into MuMu emulator
echo 3. Click install when prompted
echo 4. Try launching the app

echo.
echo ========================================
echo WebView Fix Summary
echo ========================================
echo.
echo ✅ APPLIED FIXES:
echo - Added null URL check in AgreementWebActivity.loadUrl()
echo - Modified pieperseus.py to automatically apply WebView fix
echo - Successfully rebuilt APK with WebView crash prevention
echo.
echo 🎯 EXPECTED RESULT:
echo - App should no longer crash after 3 seconds during startup
echo - Agreement page loading will be skipped if URL is null
echo - App should proceed to main game interface
echo.
echo 📋 NEXT STEPS:
echo 1. Manually install the WebView-fixed APK
echo 2. Test app startup - it should NOT crash anymore
echo 3. If still crashes, we may need additional fixes

cd ..

pause
