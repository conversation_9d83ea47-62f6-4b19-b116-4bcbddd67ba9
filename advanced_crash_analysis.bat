@echo off
chcp 65001 >nul
title Advanced Crash Analysis
color 0C

echo ========================================
echo Advanced Crash Analysis for Bilibili Version
echo ========================================

set PATH=%PATH%;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
set LOG_FILE=advanced_crash_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
set LOG_FILE=%LOG_FILE: =0%

echo [INFO] Advanced crash analysis log: %LOG_FILE%
echo ======== ADVANCED CRASH ANALYSIS ======== > %LOG_FILE%
echo Analysis Date: %date% %time% >> %LOG_FILE%
echo. >> %LOG_FILE%

echo [STEP 1] Environment check...
echo ======== ENVIRONMENT INFO ======== >> %LOG_FILE%
adb --version >> %LOG_FILE% 2>&1
echo. >> %LOG_FILE%

echo [STEP 2] Device and memory status...
echo ======== DEVICE STATUS ======== >> %LOG_FILE%
adb shell getprop ro.build.version.release >> %LOG_FILE%
adb shell cat /proc/meminfo | findstr /i "memtotal memfree memavailable" >> %LOG_FILE%
echo. >> %LOG_FILE%

echo [STEP 3] Clearing all logs and preparing...
adb logcat -c
adb disconnect 127.0.0.1:16384 >nul 2>&1

echo [STEP 4] Force stopping any existing instances...
adb shell am force-stop com.bilibili.azurlane

echo [STEP 5] Starting comprehensive monitoring...
echo Starting background monitors...

REM Start multiple specialized monitors
start "Java Exceptions" cmd /c "adb logcat -v time AndroidRuntime:E System.err:W *:S > java_exceptions.log"
start "Native Crashes" cmd /c "adb logcat -v time DEBUG:I libc:F *:S > native_crashes.log"
start "Activity Manager" cmd /c "adb logcat -v time ActivityManager:I *:S > activity_manager.log"
start "Package Manager" cmd /c "adb logcat -v time PackageManager:W *:S > package_manager.log"
start "Dalvik VM" cmd /c "adb logcat -v time dalvikvm:I art:I *:S > dalvik_vm.log"
start "System Server" cmd /c "adb logcat -v time SystemServer:W *:S > system_server.log"

timeout /t 3 /nobreak >nul

echo [STEP 6] Launching app with detailed monitoring...
echo ======== APP LAUNCH SEQUENCE ======== >> %LOG_FILE%
echo Launch time: %time% >> %LOG_FILE%

REM Launch the app
adb shell monkey -p com.bilibili.azurlane -c android.intent.category.LAUNCHER 1

echo [STEP 7] Critical monitoring window (10 seconds)...
for /L %%i in (1,1,20) do (
    echo Monitoring interval %%i/20 (0.5s each)...
    
    REM Check process status
    adb shell ps | findstr com.bilibili.azurlane >nul
    if %errorlevel% neq 0 (
        echo [CRASH DETECTED] Process died at interval %%i >> %LOG_FILE%
        echo CRASH DETECTED at interval %%i! Collecting data...
        goto :crash_analysis
    )
    
    REM Check current activity
    for /f "tokens=*" %%a in ('adb shell dumpsys activity activities ^| findstr "mResumedActivity" 2^>nul') do (
        echo %%a | findstr "bilibili.azurlane" >nul
        if %errorlevel% neq 0 (
            echo [UI CRASH] App not in foreground at interval %%i >> %LOG_FILE%
            echo UI disappeared at interval %%i! App returned to desktop.
            goto :crash_analysis
        )
    )
    
    timeout /t 1 /nobreak >nul
)

echo App survived 10 seconds! Continuing extended monitoring...
goto :success

:crash_analysis
echo.
echo ========================================
echo CRASH DETECTED! Analyzing crash data...
echo ========================================

echo ======== CRASH ANALYSIS ======== >> %LOG_FILE%
echo Crash detected at: %time% >> %LOG_FILE%

REM Wait for crash logs to be written
timeout /t 5 /nobreak >nul

echo Collecting crash information...

echo ======== PROCESS STATUS ======== >> %LOG_FILE%
adb shell ps | findstr bilibili >> %LOG_FILE%

echo ======== CURRENT ACTIVITY ======== >> %LOG_FILE%
adb shell dumpsys activity activities | findstr "mResumedActivity" >> %LOG_FILE%

echo ======== JAVA EXCEPTIONS ======== >> %LOG_FILE%
if exist java_exceptions.log (
    type java_exceptions.log >> %LOG_FILE%
) else (
    echo No Java exceptions found >> %LOG_FILE%
)

echo ======== NATIVE CRASHES ======== >> %LOG_FILE%
if exist native_crashes.log (
    type native_crashes.log >> %LOG_FILE%
) else (
    echo No native crashes found >> %LOG_FILE%
)

echo ======== ACTIVITY MANAGER LOGS ======== >> %LOG_FILE%
if exist activity_manager.log (
    type activity_manager.log >> %LOG_FILE%
) else (
    echo No activity manager logs found >> %LOG_FILE%
)

echo ======== PACKAGE MANAGER LOGS ======== >> %LOG_FILE%
if exist package_manager.log (
    type package_manager.log >> %LOG_FILE%
) else (
    echo No package manager logs found >> %LOG_FILE%
)

echo ======== DALVIK VM LOGS ======== >> %LOG_FILE%
if exist dalvik_vm.log (
    type dalvik_vm.log >> %LOG_FILE%
) else (
    echo No Dalvik VM logs found >> %LOG_FILE%
)

echo ======== SYSTEM SERVER LOGS ======== >> %LOG_FILE%
if exist system_server.log (
    type system_server.log >> %LOG_FILE%
) else (
    echo No system server logs found >> %LOG_FILE%
)

echo ======== RECENT FULL LOGCAT ======== >> %LOG_FILE%
adb logcat -d -t 300 >> %LOG_FILE%

goto :cleanup

:success
echo ======== APP STARTUP SUCCESSFUL ======== >> %LOG_FILE%
echo App survived the critical startup period >> %LOG_FILE%

:cleanup
REM Stop all background monitors
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Java Exceptions" >nul 2>&1
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Native Crashes" >nul 2>&1
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Activity Manager" >nul 2>&1
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Package Manager" >nul 2>&1
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Dalvik VM" >nul 2>&1
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq System Server" >nul 2>&1

REM Clean up temporary files
del java_exceptions.log 2>nul
del native_crashes.log 2>nul
del activity_manager.log 2>nul
del package_manager.log 2>nul
del dalvik_vm.log 2>nul
del system_server.log 2>nul

echo.
echo ========================================
echo Analysis completed!
echo Check %LOG_FILE% for detailed crash information
echo ========================================

echo.
echo Key findings:
findstr /i "crash fatal error exception caused" %LOG_FILE%

pause
