#!/usr/bin/env python3
"""
WebView崩溃修复脚本
修复AgreementWebActivity中的null URL问题
"""

import os
import re
import shutil
import logging

logging.basicConfig(level=logging.INFO)

def find_agreement_activity():
    """查找AgreementWebActivity相关文件"""
    agreement_files = []
    
    # 在反编译的APK目录中查找
    for root, dirs, files in os.walk('apk_build'):
        for file in files:
            if 'Agreement' in file and file.endswith('.smali'):
                agreement_files.append(os.path.join(root, file))
    
    return agreement_files

def patch_agreement_webview(file_path):
    """修复WebView URL为null的问题"""
    logging.info(f"Patching {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原文件
    shutil.copy(file_path, file_path + '.backup')
    
    # 查找loadUrl方法调用
    # 将null URL替换为空字符串或跳过加载
    patterns = [
        # 匹配 loadUrl(null) 调用
        (r'invoke-virtual.*loadUrl.*const/4.*null', 
         'return-void  # Skip WebView loading to prevent crash'),
        
        # 匹配空URL检查
        (r'if-eqz.*loadUrl', 
         'return-void  # Skip if URL is null'),
    ]
    
    modified = False
    for pattern, replacement in patterns:
        if re.search(pattern, content, re.IGNORECASE):
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            modified = True
            logging.info(f"Applied pattern: {pattern}")
    
    # 如果没有找到特定模式，添加通用的null检查
    if not modified:
        # 在loadUrl方法开始处添加null检查
        loadurl_pattern = r'(\.method.*loadUrl.*\n)(.*?)(\.end method)'
        match = re.search(loadurl_pattern, content, re.DOTALL)
        if match:
            method_start = match.group(1)
            method_body = match.group(2)
            method_end = match.group(3)
            
            # 添加null检查
            null_check = """
    # Check if URL is null to prevent crash
    if-eqz p1, :skip_load
    
    # Original method body continues here
"""
            skip_label = """
    
    :skip_load
    return-void
"""
            
            new_content = method_start + null_check + method_body + skip_label + method_end
            content = re.sub(loadurl_pattern, new_content, content, flags=re.DOTALL)
            modified = True
            logging.info("Added null URL check")
    
    if modified:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logging.info(f"Successfully patched {file_path}")
        return True
    else:
        logging.warning(f"No modifications made to {file_path}")
        return False

def main():
    """主函数"""
    logging.info("Starting WebView crash fix...")
    
    # 查找协议相关文件
    agreement_files = find_agreement_activity()
    
    if not agreement_files:
        logging.error("No Agreement activity files found!")
        logging.info("Make sure you have decompiled the APK first:")
        logging.info("python pieperseus.py --no-skip")
        return False
    
    logging.info(f"Found {len(agreement_files)} agreement files:")
    for file in agreement_files:
        logging.info(f"  - {file}")
    
    # 修复每个文件
    patched_count = 0
    for file_path in agreement_files:
        if patch_agreement_webview(file_path):
            patched_count += 1
    
    if patched_count > 0:
        logging.info(f"Successfully patched {patched_count} files")
        logging.info("Now rebuild the APK:")
        logging.info("python pieperseus.py")
        return True
    else:
        logging.error("No files were patched!")
        return False

if __name__ == "__main__":
    main()
